#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
[V59.0] 独立素材收割机制

不依赖申诉结果的素材状态检查和收割机制。
定期扫描所有计划中的素材，检查审核状态并及时收割通过的素材。
"""

import os
import json
import time
import shutil
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
from sqlalchemy.orm import Session, joinedload
from collections import defaultdict

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import Campaign, PlatformCreative, AdAccount, Principal
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.exceptions import QianchuanAPIError

def scan_and_harvest_materials(db: Session, app_settings: Dict[str, Any]):
    """
    [V59.0] 独立素材收割主函数
    扫描所有计划中的素材，检查审核状态并收割通过的素材
    """
    logger.info("--- [独立素材收割] 开始扫描所有计划中的素材状态 ---")

    try:
        # 获取收割范围配置
        harvest_config = app_settings.get('workflow', {}).get('independent_harvest', {})
        harvest_scope = harvest_config.get('scope', {})

        # 🚨 安全检查：只处理测试账户
        test_accounts_only = harvest_scope.get('test_accounts_only', True)
        account_type_filter = harvest_scope.get('account_type_filter', ['TEST'])

        if test_accounts_only:
            logger.info(f"🛡️ 素材收割安全模式：仅处理测试账户类型 {account_type_filter}")
            # 获取需要检查的计划，只包含测试账户
            campaigns_to_check = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal),
                joinedload(Campaign.platform_creatives)
            ).join(AdAccount).filter(
                Campaign.status.in_(['AUDITING', 'REJECTED', 'MONITORING', 'RUNNING']),
                AdAccount.account_type.in_(account_type_filter)
            ).all()
        else:
            logger.warning("⚠️ 素材收割非安全模式：处理所有账户类型")
            # 获取所有需要检查的计划（排除已完成的）
            campaigns_to_check = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal),
                joinedload(Campaign.platform_creatives)
            ).filter(
                Campaign.status.in_(['AUDITING', 'REJECTED', 'MONITORING', 'RUNNING'])
            ).all()
        
        if not campaigns_to_check:
            logger.info("没有需要检查的计划")
            return
        
        logger.info(f"找到 {len(campaigns_to_check)} 个计划需要检查素材状态")
        
        # 按账户分组处理
        campaigns_by_account = defaultdict(list)
        for campaign in campaigns_to_check:
            campaigns_by_account[campaign.account].append(campaign)
        
        total_harvested = 0
        
        for account, campaigns in campaigns_by_account.items():
            principal = account.principal

            # 🚨 双重安全检查：确保只处理测试账户
            if test_accounts_only and account.account_type not in account_type_filter:
                logger.warning(f"⚠️ 跳过非测试账户: {account.name} (类型: {account.account_type})")
                continue

            logger.info(f"✅ 检查测试账户 {account.name} (类型: {account.account_type}) 下的 {len(campaigns)} 个计划")

            try:
                client = QianchuanClient(
                    app_id=app_settings['api_credentials']['app_id'],
                    secret=app_settings['api_credentials']['secret'],
                    principal_id=principal.id
                )
                
                for campaign in campaigns:
                    harvested_count = _check_and_harvest_campaign_materials(
                        db, client, campaign, principal, app_settings
                    )
                    total_harvested += harvested_count
                    
                    # 避免API频率限制
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"处理账户 {account.name} 时发生错误: {e}", exc_info=True)
                continue
        
        logger.success(f"--- [独立素材收割] 完成，共收割 {total_harvested} 个素材 ---")
        
    except Exception as e:
        logger.error(f"独立素材收割发生严重错误: {e}", exc_info=True)
        db.rollback()

def _check_and_harvest_campaign_materials(
    db: Session, 
    client: QianchuanClient, 
    campaign: Campaign, 
    principal: Principal, 
    app_settings: Dict[str, Any]
) -> int:
    """检查并收割单个计划中的素材"""
    harvested_count = 0
    
    try:
        # 获取计划中的所有素材状态
        materials_in_ad = client.get_materials_in_ad(
            advertiser_id=int(campaign.account.account_id_qc),
            ad_id=int(campaign.campaign_id_qc)
        )
        
        if not materials_in_ad:
            logger.debug(f"计划 {campaign.campaign_id_qc} 无法获取素材信息")
            return 0
        
        # 使用原有成功的数据结构解析逻辑
        logger.debug(f"计划 {campaign.campaign_id_qc} 获取到 {len(materials_in_ad)} 个素材")

        # 直接使用原有scheduler.py中的成功逻辑，不创建映射
        for material_info in materials_in_ad:
            # 使用原有的嵌套路径获取material_id
            material_id = material_info.get("material_info", {}).get("video_material", {}).get("material_id")
            if not material_id:
                continue

            # 查找对应的平台素材记录
            pc = db.query(PlatformCreative).filter(
                PlatformCreative.material_id_qc == str(material_id)
            ).first()
            if not pc:
                continue

            # 获取审核状态
            audit_status = material_info.get("audit_status")

            if audit_status == 'PASS':
                # 素材审核通过，检查是否已经收割
                if pc.local_creative and pc.local_creative.status != 'approved':
                    logger.info(f"发现通过审核的素材: {material_id} (计划: {campaign.campaign_id_qc})")

                    # 执行收割
                    if _harvest_approved_material(db, pc, principal, app_settings):
                        harvested_count += 1

            elif audit_status in ['REJECT', 'AUDIT_REJECT']:
                # 素材被拒绝，更新状态
                if pc.local_creative and pc.local_creative.status != 'rejected':
                    logger.warning(f"素材 {material_id} 在计划 {campaign.campaign_id_qc} 中被拒绝 (状态: {audit_status})。")
                    pc.local_creative.status = 'rejected'
                    db.commit()

        return harvested_count
        
    except QianchuanAPIError as e:
        logger.warning(f"获取计划 {campaign.campaign_id_qc} 素材状态失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"检查计划 {campaign.campaign_id_qc} 素材时发生错误: {e}", exc_info=True)
        return 0

def _harvest_approved_material(
    db: Session,
    platform_creative: PlatformCreative,
    principal: Principal,
    app_settings: Dict[str, Any]
) -> bool:
    """
    [V59.1] 收割通过审核的素材 - 包含完整的文件移动和弹药库更新功能
    复制原有_add_to_approved_library的完整逻辑
    """
    try:
        local_creative = platform_creative.local_creative
        if not local_creative:
            logger.warning(f"平台素材 {platform_creative.material_id_qc} 缺少本地素材关联")
            return False

        # 检查是否已经是approved状态（幂等性检查）
        if local_creative.status == 'approved':
            logger.debug(f"素材 {platform_creative.material_id_qc} 已经是approved状态，跳过收割")
            return False

        # 更新本地素材状态
        local_creative.status = 'approved'
        db.commit()

        # 执行文件移动和弹药库更新
        _move_file_to_approved_dir(platform_creative, principal, app_settings)
        _add_to_ammunition_library(platform_creative, principal, app_settings)

        logger.success(f"成功收割素材 {platform_creative.material_id_qc}")
        return True

    except Exception as e:
        logger.error(f"收割素材 {platform_creative.material_id_qc} 失败: {e}", exc_info=True)
        db.rollback()
        return False

def _move_file_to_approved_dir(
    platform_creative: PlatformCreative,
    principal: Principal,
    app_settings: Dict[str, Any]
):
    """
    [V59.1] 将通过审核的素材文件移动到03_materials_approved目录
    复制原有_add_to_approved_library中的文件移动逻辑
    """
    try:
        local_creative = platform_creative.local_creative
        if not local_creative or not local_creative.file_path:
            logger.warning(f"素材 {platform_creative.material_id_qc} 缺少文件路径，无法执行文件移动")
            return

        # 路径准备
        date_str = datetime.now().strftime('%Y-%m-%d')
        principal_name = principal.name
        filename = os.path.basename(local_creative.file_path)

        # 获取工作流目录配置
        base_workflow_dir = app_settings.get('custom_workflow_assets_dir')
        if not base_workflow_dir:
            base_workflow_dir = app_settings.get('workflow_assets_dir', 'G:/workflow_assets')

        workflow_dirs = app_settings.get('workflow_dirs', {})
        archive_dir = os.path.join(base_workflow_dir, workflow_dirs.get('DIR_00_ARCHIVED', '00_materials_archived'), principal_name)
        approved_dir = os.path.join(base_workflow_dir, workflow_dirs.get('DIR_03_MATERIALS_APPROVED', '03_materials_approved'), principal_name, date_str)

        source_path = os.path.join(archive_dir, filename)
        target_path = os.path.join(approved_dir, filename)

        # 执行文件复制
        if not os.path.exists(target_path):
            if not os.path.exists(source_path):
                logger.error(f"文件移动失败：在源归档目录 '{archive_dir}' 中找不到文件 '{filename}'")
                # 尝试使用当前文件路径作为源
                if os.path.exists(local_creative.file_path):
                    source_path = local_creative.file_path
                    logger.info(f"使用当前文件路径作为源: {source_path}")
                else:
                    logger.error(f"文件移动失败：文件 '{filename}' 在任何位置都找不到")
                    return

            # 创建目标目录并复制文件
            os.makedirs(approved_dir, exist_ok=True)
            shutil.copy(source_path, target_path)
            logger.success(f"文件已成功复制到: {target_path}")
        else:
            logger.info(f"目标文件 '{target_path}' 已存在，跳过复制")

    except Exception as e:
        logger.error(f"移动素材文件 {platform_creative.material_id_qc} 时发生错误: {e}", exc_info=True)

def _add_to_ammunition_library(
    platform_creative: PlatformCreative,
    principal: Principal,
    app_settings: Dict[str, Any]
):
    """
    [V59.1] 添加素材到弹药库 - 与原有格式保持一致
    复制原有_add_to_approved_library中的JSON更新逻辑
    """
    try:
        # 获取弹药库路径
        base_workflow_dir = app_settings.get('custom_workflow_assets_dir')
        if not base_workflow_dir:
            base_workflow_dir = app_settings.get('workflow_assets_dir', 'G:/workflow_assets')

        library_path = os.path.join(base_workflow_dir, "approved_creatives.json")

        # 读取现有弹药库
        library = []
        if os.path.exists(library_path):
            with open(library_path, 'r', encoding='utf-8') as f:
                try:
                    library = json.load(f)
                except json.JSONDecodeError:
                    library = []

        # 检查是否已存在（使用与原有代码一致的字段名）
        material_id = platform_creative.material_id_qc
        if any(item.get('material_id_qc') == material_id or item.get('material_id') == material_id for item in library):
            logger.debug(f"素材 {material_id} 已存在于弹药库中")
            return

        # 准备文件路径信息
        local_creative = platform_creative.local_creative
        filename = os.path.basename(local_creative.file_path) if local_creative and local_creative.file_path else f"material_{material_id}"

        # 计算目标文件路径（与文件移动逻辑保持一致）
        date_str = datetime.now().strftime('%Y-%m-%d')
        workflow_dirs = app_settings.get('workflow_dirs', {})
        approved_dir = os.path.join(base_workflow_dir, workflow_dirs.get('DIR_03_MATERIALS_APPROVED', '03_materials_approved'), principal.name, date_str)
        target_path = os.path.join(approved_dir, filename)

        # 添加新素材信息（使用与原有代码一致的格式）
        library.append({
            "素材名称": filename,
            "审核通过日期时间": datetime.now().isoformat(),
            "material_id": material_id,  # 保持与原有代码一致的字段名
            "所属主体": principal.name,
            "测审广告户": platform_creative.account.name,
            "video_url": platform_creative.video_url,
            "file_path_in_approved": target_path,  # 添加文件路径信息
            "收割方式": "独立扫描收割"
        })

        # 保存弹药库
        os.makedirs(os.path.dirname(library_path), exist_ok=True)
        with open(library_path, 'w', encoding='utf-8') as f:
            json.dump(library, f, indent=4, ensure_ascii=False)

        logger.success(f"素材 {material_id} 已添加到弹药库")

    except Exception as e:
        logger.error(f"添加素材 {platform_creative.material_id_qc} 到弹药库失败: {e}", exc_info=True)

def get_harvest_statistics(db: Session) -> Dict[str, Any]:
    """获取收割统计信息"""
    try:
        # 统计各状态的素材数量
        from qianchuan_aw.database.models import LocalCreative
        
        stats = {
            'total_materials': db.query(LocalCreative).count(),
            'approved_materials': db.query(LocalCreative).filter(LocalCreative.status == 'approved').count(),
            'rejected_materials': db.query(LocalCreative).filter(LocalCreative.status == 'rejected').count(),
            'pending_materials': db.query(LocalCreative).filter(LocalCreative.status == 'uploaded').count(),
        }
        
        # 读取弹药库统计
        try:
            from qianchuan_aw.database.database import load_settings
            app_settings = load_settings()
            base_workflow_dir = app_settings.get('workflow_assets_dir', 'G:/workflow_assets')
            workflow_assets_dir = app_settings.get('custom_workflow_assets_dir', base_workflow_dir)
            library_path = os.path.join(workflow_assets_dir, "approved_creatives.json")
            
            if os.path.exists(library_path):
                with open(library_path, 'r', encoding='utf-8') as f:
                    library = json.load(f)
                    stats['ammunition_library_count'] = len(library)
            else:
                stats['ammunition_library_count'] = 0
        except Exception:
            stats['ammunition_library_count'] = 0
        
        return stats
        
    except Exception as e:
        logger.error(f"获取收割统计失败: {e}")
        return {}

if __name__ == "__main__":
    # 独立运行测试
    from qianchuan_aw.utils.db_utils import database_session
    from qianchuan_aw.database.database import load_settings
    
    app_settings = load_settings()
    
    with database_session() as db:
        scan_and_harvest_materials(db, app_settings)
