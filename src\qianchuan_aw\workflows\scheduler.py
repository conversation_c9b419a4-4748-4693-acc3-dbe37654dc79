# -*- coding: utf-8 -*-
"""
[V52.3] 全自动工作流调度器核心逻辑 (同步最终版)
"""
import time
import os
import json
import shutil
import random
from datetime import datetime, timedelta, timezone
from collections import defaultdict
import os
import json
import random
import shutil
import time

from sqlalchemy.orm import joinedload, Session
from sqlalchemy import or_

from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import (AdAccount, Campaign, LocalCreative,
                                          PlatformCreative, Principal,
                                          campaign_platform_creative_association)
from qianchuan_aw.sdk_qc.client import QianchuanClient, QianchuanAPIException
from qianchuan_aw.workflows.common.plan_creation import create_ad_plan
from qianchuan_aw.workflows.flexible_grouping import create_flexible_groups, check_grouping_timeout, should_force_create_plan, get_flexible_grouping_config
from qianchuan_aw.services.copilot_service import CopilotSession
from qianchuan_aw.utils.file_utils import get_file_md5
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.workflow_helpers import (
    check_rate_limit, extract_cover_id_from_url,
    find_or_create_local_creative, get_random_value_from_range,
    record_plan_creation, rollback_and_reset_creatives)
# [Refactor] 导入tasks模块用于派发子任务
from qianchuan_aw.workflows import tasks
from typing import Dict, Any


last_used_account_index = defaultdict(int)


def set_account_status(account: AdAccount, status: str, reason: str):
    from qianchuan_aw.utils.db_utils import database_session

    with database_session() as db:
        acc_to_update = db.query(AdAccount).filter(AdAccount.id == account.id).first()
        if acc_to_update:
            acc_to_update.status = status
            logger.warning(f"账户 {account.name} 的状态已设置为 '{status}'。原因: {reason}")
        else:
            logger.error(f"未找到账户 {account.name} (ID: {account.id})")


def is_account_healthy(db: Session, client: QianchuanClient, account: AdAccount) -> bool:
    try:
        if account.status == 'deleted':
            return False
        
        if account.status == 'temporarily_blocked':
            if account.blocked_until and datetime.now(timezone.utc) > account.blocked_until:
                account.status = 'active'
                account.blocked_until = None
                db.add(account)
                db.commit()
                logger.success(f"账户 {account.name} 已自动解封，状态恢复为 'active'。")
            else:
                return False
        
        disposal_info = client.get_disposal_info(advertiser_id=account.account_id_qc)
        if disposal_info and disposal_info.get("disposal_info_list"):
            set_account_status(account, "deleted", "查询到官方处罚记录 (硬性封停)")
            return False
        
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=5)
        
        violation_events = client.get_score_violation_event(
            advertiser_id=account.account_id_qc,
            start_time=start_date.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_date.strftime('%Y-%m-%d %H:%M:%S'),
            filtering={"status": "VALID"}
        )
 
        if violation_events:
            for event in violation_events:
                if (event.get("score") == 4 and
                    "中度撞审" in event.get("reject_reason", "") and
                    event.get("illegal_type") == "TWOTHREECLASS"):
                    
                    event_create_time_naive = datetime.strptime(event["create_time"], '%Y-%m-%d %H:%M:%S')
                    event_create_time_aware = event_create_time_naive.replace(tzinfo=timezone.utc)
 
                    if datetime.now(timezone.utc) - event_create_time_aware < timedelta(days=3):
                        account.status = 'temporarily_blocked'
                        account.blocked_until = datetime.now(timezone.utc) + timedelta(days=3)
                        db.add(account)
                        db.commit()
                        logger.critical(f"账户 {account.name} 检测到中度撞审违规（+4分），已临时封禁3天至 {account.blocked_until}。")
                        return False
            
        return True
 
    except Exception as e:
        logger.error(f"检查账户 {account.name} 健康度时发生错误: {e}", exc_info=True)
        db.rollback()
        return False
 
def _add_to_approved_library(db: Session, creative: PlatformCreative, app_settings: Dict[str, Any]):
    """
    [V2025.07.24 - 修复版] 将通过审核的素材收割到03目录，并确保操作的原子性和幂等性。
    修复：使用新的工作流文件管理器，确保文件移动到正确的收割目录。
    """
    if not creative.local_creative:
        logger.warning(f"素材 PlatformCreative ID: {creative.id} 缺少本地素材关联，无法收割。")
        return

    # Use a new session for this atomic operation to not interfere with the caller's session
    session = SessionLocal()
    try:
        from qianchuan_aw.utils.workflow_file_operations import get_workflow_file_manager, atomic_file_operation
        from qianchuan_aw.utils.workflow_status import WorkflowStatus

        # --- 1. Atomic State Transition and File Harvest ---
        with atomic_file_operation(session, f"harvest_material_{creative.local_creative.id}"):
            # Lock the row for the duration of the transaction
            local_creative_locked = session.query(LocalCreative).filter(
                LocalCreative.id == creative.local_creative.id
            ).with_for_update().first()

            if not local_creative_locked:
                logger.error(f"Database integrity error: Could not find LocalCreative with id {creative.local_creative.id} to harvest.")
                return

            # [V2025.07.24 - 业务逻辑修正版] 严格的幂等性检查
            # 检查是否已经收割过（基于material_id_qc的唯一性）
            from qianchuan_aw.utils.workflow_quality_control import get_quality_controller

            quality_controller = get_quality_controller(session)
            harvest_check = quality_controller.check_harvest_uniqueness(creative.material_id_qc)

            if harvest_check['already_harvested']:
                logger.info(f"素材 material_id_qc: {creative.material_id_qc} 已收割过，跳过重复收割")
                logger.info(f"   已收割详情: {harvest_check['harvest_details']}")
                return

            # 检查当前状态是否为approved（只有approved状态才能收割）
            if local_creative_locked.status != WorkflowStatus.APPROVED.value:
                logger.warning(f"素材 LocalCreative ID: {local_creative_locked.id} 状态为 {local_creative_locked.status}，不是approved状态，无法收割")
                return

            # 获取文件管理器并执行收割
            file_manager = get_workflow_file_manager(app_settings)
            principal_name = creative.account.principal.name

            if local_creative_locked.file_path and os.path.exists(local_creative_locked.file_path):
                # 移动文件到收割目录
                harvest_path, operation_info = file_manager.move_to_harvest(
                    source_path=local_creative_locked.file_path,
                    principal_name=principal_name,
                    preserve_original=False  # 移动文件，不保留原文件
                )

                # 更新数据库状态和文件路径
                local_creative_locked.status = WorkflowStatus.HARVESTED.value
                local_creative_locked.file_path = harvest_path

                logger.success(f"✅ 素材收割成功！LocalCreative ID: {local_creative_locked.id}")
                logger.info(f"   收割路径: {harvest_path}")
                logger.info(f"   状态: {WorkflowStatus.HARVESTED.value}")
            else:
                # 文件不存在，只更新状态
                logger.warning(f"素材文件不存在: {local_creative_locked.file_path}，仅更新状态")
                local_creative_locked.status = WorkflowStatus.HARVESTED.value
                harvest_path = None

        # --- 2. Update JSON Library (Post-Commit) ---
        # 更新JSON弹药库
        try:
            base_workflow_dir = app_settings.get('custom_workflow_assets_dir')
            if not base_workflow_dir:
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                base_workflow_dir = os.path.join(project_root, 'workflow_assets')

            library_path = os.path.join(base_workflow_dir, "approved_creatives.json")
            library = []
            if os.path.exists(library_path):
                with open(library_path, 'r', encoding='utf-8') as f:
                    try:
                        library = json.load(f)
                    except json.JSONDecodeError:
                        library = []

            # 检查是否已存在，避免重复添加
            if not any(item.get('material_id_qc') == creative.material_id_qc for item in library):
                filename = os.path.basename(harvest_path) if harvest_path else "文件不存在"
                library.append({
                    "素材名称": filename,
                    "审核通过日期时间": datetime.now().isoformat(),
                    "material_id_qc": creative.material_id_qc,
                    "所属主体": principal_name,
                    "测审广告户": creative.account.name,
                    "video_url": creative.video_url,
                    "file_path_in_harvest": harvest_path,
                    "status": WorkflowStatus.HARVESTED.value
                })

                with open(library_path, 'w', encoding='utf-8') as f:
                    json.dump(library, f, indent=4, ensure_ascii=False)
                logger.success(f"素材 {creative.material_id_qc} 的信息已成功写入JSON弹药库。")
            else:
                logger.info(f"素材 {creative.material_id_qc} 已存在于JSON弹药库中，跳过写入。")

        except Exception as e_json:
            logger.error(f"更新JSON弹药库时发生错误: {e_json}", exc_info=True)

    except Exception as e:
        logger.error(f"归档素材 PlatformCreative ID: {creative.id} 的过程中发生严重错误: {e}", exc_info=True)
        # No rollback needed here because session.begin() handles it
    finally:
        session.close()
 
def process_single_video_upload(db: Session, app_settings: Dict[str, Any], local_creative_id: int, account_id: int, file_path: str, principal_name: str):
    """
    [Worker Logic] 处理单个视频的上传、注册和归档。
    这是一个独立的、可被Celery任务调用的函数。
    """
    try:
        # [V2025.07.24 - 业务逻辑修正版] 使用新的质量控制系统
        from qianchuan_aw.utils.workflow_quality_control import get_quality_controller
        from qianchuan_aw.utils.workflow_status import WorkflowStatus

        # 获取本地素材记录
        local_creative = db.get(LocalCreative, local_creative_id)
        if not local_creative:
            raise ValueError(f"找不到本地素材 ID: {local_creative_id}")

        # === 第一步：素材质量检查 ===
        quality_controller = get_quality_controller(db)
        quality_result = quality_controller.check_material_quality(file_path)

        if quality_result['delete_required']:
            # 素材质量问题，直接删除文件，不设置失败状态
            logger.warning(f"素材质量不合格，删除文件: {quality_result['delete_reason']}")
            delete_result = quality_controller.delete_material_with_reason(
                file_path=file_path,
                reason=quality_result['delete_reason'],
                creative_id=local_creative_id
            )

            if delete_result['deleted']:
                logger.info(f"✅ 质量不合格素材已删除: {os.path.basename(file_path)}")
                return  # 直接返回，不设置任何状态
            else:
                logger.error(f"删除质量不合格素材失败: {delete_result.get('error', 'Unknown error')}")
                raise Exception(f"删除质量不合格素材失败: {delete_result.get('error')}")

        logger.success(f"✅ 素材质量检查通过: {os.path.basename(file_path)}")

        client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=db.query(Principal).filter_by(name=principal_name).first().id)
        account = db.get(AdAccount, account_id)
        if not account:
            raise ValueError(f"数据库完整性错误：在任务开始时找不到内部ID为 {account_id} 的账户。")

        try:
            # [V-Fix-4] 添加文件锁检查和重试机制
            import time
            import random

            # 标准化文件路径
            normalized_path = os.path.normpath(file_path)
            if not os.path.exists(normalized_path):
                raise FileNotFoundError(f"文件不存在: {normalized_path}")

            # 文件锁重试机制
            max_retries = 3
            file_hash = None
            for attempt in range(max_retries):
                try:
                    # 尝试访问文件并计算MD5
                    file_hash = get_file_md5(normalized_path)
                    logger.debug(f"成功计算文件MD5: {os.path.basename(normalized_path)} -> {file_hash[:8]}...")
                    break  # 成功，跳出重试循环

                except (PermissionError, OSError) as lock_error:
                    if attempt < max_retries - 1:
                        wait_time = random.uniform(1, 3) * (attempt + 1)
                        logger.warning(f"文件被锁定，等待 {wait_time:.1f}s 后重试 (尝试 {attempt + 1}/{max_retries}): {normalized_path}")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"文件锁定冲突，已重试 {max_retries} 次仍无法访问: {normalized_path}")
                        raise lock_error

        except (FileNotFoundError, IOError, ValueError) as e:
            logger.error(f"MD5计算失败 - 文件: {file_path}, 错误: {e}")
            raise ValueError(f"无法计算文件MD5: {file_path}") from e

        # --- 上传视频 ---
        upload_response_data = None
        current_retry_delay = app_settings['robustness']['upload_retry_delay']
        for attempt in range(app_settings['robustness']['max_retries_for_upload'] + 1):
            try:
                # [V-Fix-4] 上传前再次检查文件锁
                try:
                    with open(normalized_path, 'rb') as test_file:
                        test_file.read(1024)  # 测试文件是否可读
                except (PermissionError, OSError) as lock_error:
                    logger.warning(f"上传前文件锁检查失败: {normalized_path}, 错误: {lock_error}")
                    if attempt < app_settings['robustness']['max_retries_for_upload']:
                        wait_time = random.uniform(2, 5)
                        logger.info(f"等待 {wait_time:.1f}s 后重试上传")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise lock_error

                upload_response_data = client.upload_video(
                    advertiser_id=account.account_id_qc,
                    video_file_path=normalized_path,  # 使用标准化路径
                    video_signature=file_hash
                )
                if upload_response_data: break

            except QianchuanAPIException as e:
                logger.warning(f"上传视频 '{os.path.basename(file_path)}' 失败 (尝试 {attempt + 1}/{app_settings['robustness']['max_retries_for_upload'] + 1}): {e}")
                if attempt < app_settings['robustness']['max_retries_for_upload']:
                    time.sleep(current_retry_delay)
                    current_retry_delay *= 2
                else:
                    raise  # 重试耗尽后，抛出异常由Celery处理
            except (PermissionError, OSError) as file_error:
                logger.error(f"文件访问错误 '{os.path.basename(file_path)}' (尝试 {attempt + 1}/{app_settings['robustness']['max_retries_for_upload'] + 1}): {file_error}")
                if attempt < app_settings['robustness']['max_retries_for_upload']:
                    wait_time = random.uniform(3, 6)
                    logger.info(f"文件锁冲突，等待 {wait_time:.1f}s 后重试")
                    time.sleep(wait_time)
                else:
                    raise  # 重试耗尽后，抛出异常

        if not upload_response_data:
            raise ValueError(f"视频上传API在所有重试后仍未返回任何数据: {file_path}")

        video_id = upload_response_data.get("video_id")
        material_id = upload_response_data.get("material_id")
        if not video_id or not material_id:
            raise ValueError(f"视频上传API返回的数据中缺少 video_id 或 material_id: {upload_response_data}")

        # --- 反查封面 ---
        max_retries = app_settings['robustness']['max_retries_for_cover']
        retry_delay = app_settings['robustness']['initial_retry_delay']
        library_video_data = None
        for attempt in range(max_retries):
            time.sleep(retry_delay)
            try:
                library_videos_response = client.get_library_videos(advertiser_id=account.account_id_qc, filtering={"video_ids": [video_id]})
                # get_library_videos 返回的是一个字典，包含 'list' 字段
                library_videos = library_videos_response.get('list', []) if library_videos_response else []
                if library_videos and 'poster_url' in library_videos[0] and library_videos[0]['poster_url']:
                    library_video_data = library_videos[0]
                    break
                logger.warning(f"第 {attempt + 1}/{max_retries} 次反查 video_id '{video_id}' 未获取到封面，准备重试...")
                retry_delay += 5
            except Exception as e_check:
                logger.warning(f"第 {attempt + 1}/{max_retries} 次反查时出错: {e_check}")

        if not library_video_data:
            raise ValueError(f"在 {max_retries} 次尝试后，仍无法为 video_id '{video_id}' 获取到封面信息。")

        video_cover_id = extract_cover_id_from_url(library_video_data.get("poster_url"))
        if not video_cover_id:
            raise ValueError(f"无法为 video_id '{video_id}' 从URL '{library_video_data.get('poster_url')}' 提取封面ID。")

        # --- 原子性更新数据库和文件移动 ---
        from qianchuan_aw.utils.workflow_file_operations import get_workflow_file_manager, atomic_file_operation
        from qianchuan_aw.utils.workflow_status import WorkflowStatus

        with atomic_file_operation(db, f"upload_and_archive_{local_creative_id}"):
            # 获取文件管理器
            file_manager = get_workflow_file_manager(app_settings)

            # 移动文件到存档目录
            archive_path, operation_info = file_manager.move_to_archive(
                source_path=normalized_path,
                principal_name=principal_name,
                preserve_original=False
            )

            # 创建平台素材记录
            platform_creative = PlatformCreative(
                local_creative_id=local_creative_id,
                account_id=account.id,
                material_id_qc=str(material_id),
                video_id=video_id,
                video_url=library_video_data.get("url"),
                video_cover_id=video_cover_id,
                review_status='AUDITING'
            )
            db.add(platform_creative)

            # 更新本地素材记录
            local_creative = db.get(LocalCreative, local_creative_id)
            local_creative.status = WorkflowStatus.UPLOADED_PENDING_PLAN.value
            local_creative.video_id = video_id
            local_creative.material_id_qc = str(material_id)
            local_creative.uploaded_to_account_id = account.id
            local_creative.file_path = archive_path  # 更新文件路径到存档位置

            # 提交所有更改
            db.commit()

            logger.success(f"✅ 视频 '{os.path.basename(file_path)}' (video_id: {video_id}) 已成功上传并存档")
            logger.info(f"   原路径: {normalized_path}")
            logger.info(f"   存档路径: {archive_path}")
            logger.info(f"   状态: {WorkflowStatus.UPLOADED_PENDING_PLAN.value}")

    except Exception as e:
        logger.error(f"处理单个视频上传任务时发生错误 (File: {file_path}): {e}", exc_info=True)

        # === 错误分类处理 ===
        error_msg = str(e).lower()

        # 检查是否为素材质量问题（应该删除文件）
        quality_errors = [
            '视频长度需大于', '视频时长', 'duration',           # 时长问题
            '格式不支持', 'format not supported', 'invalid format',  # 格式问题
            '文件损坏', 'file corrupted', 'invalid file',      # 文件损坏
            '文件大小', 'file size', 'too large', 'too small'  # 大小问题
        ]

        # 检查是否为技术性临时问题
        technical_errors = [
            'rate limit', 'limit exceeded', 'too many requests',  # API限流
            'timeout', 'connection', 'network',                   # 网络问题
            'server error', '500', '502', '503', '504',          # 服务器错误
            'service unavailable', 'temporarily unavailable'      # 服务不可用
        ]

        is_quality_error = any(keyword in error_msg for keyword in quality_errors)
        is_technical_error = any(keyword in error_msg for keyword in technical_errors)

        try:
            if is_quality_error:
                # 素材质量问题，删除文件，不设置失败状态
                logger.warning(f"检测到素材质量问题，删除文件: {e}")
                quality_controller = get_quality_controller(db)
                delete_result = quality_controller.delete_material_with_reason(
                    file_path=file_path,
                    reason=f"API返回质量错误: {e}",
                    creative_id=local_creative_id
                )

                if delete_result['deleted']:
                    logger.info(f"✅ 质量问题素材已删除: {os.path.basename(file_path)}")
                    return  # 直接返回，不抛出异常
                else:
                    logger.error(f"删除质量问题素材失败: {delete_result.get('error')}")

            elif is_technical_error:
                # 技术性临时问题，设置失败状态，进入重试流程
                logger.warning(f"检测到技术性临时问题，设置重试状态: {e}")
                db.query(LocalCreative).filter_by(id=local_creative_id).update({
                    "status": WorkflowStatus.UPLOAD_FAILED.value
                })
                db.commit()
            else:
                # 其他未知错误，也设置失败状态
                logger.error(f"视频上传过程中发生未知错误: {e}")
                db.query(LocalCreative).filter_by(id=local_creative_id).update({
                    "status": WorkflowStatus.UPLOAD_FAILED.value
                })
                db.commit()
        except Exception as db_error:
            logger.error(f"更新数据库状态失败: {db_error}")

        raise  # 重新抛出异常，让Celery知道任务失败了


def handle_failed_upload_recovery(db: Session, app_settings: Dict[str, Any]):
    """
    [V-Fix-1] 处理上传失败的素材恢复
    1. 查找所有 upload_failed 状态的素材
    2. 检查文件是否存在于归档文件夹中
    3. 将文件移回处理文件夹并重置状态
    """
    logger.info("--- [失败素材恢复] 开始处理上传失败的素材 ---")

    try:
        # 查找所有上传失败的素材
        failed_creatives = db.query(LocalCreative).filter(
            LocalCreative.status == 'upload_failed'
        ).all()

        if not failed_creatives:
            logger.info("没有发现上传失败的素材，跳过恢复流程。")
            return

        logger.info(f"发现 {len(failed_creatives)} 个上传失败的素材，开始恢复处理...")

        base_workflow_dir = app_settings.get('custom_workflow_assets_dir')
        if not base_workflow_dir:
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            base_workflow_dir = os.path.join(project_root, 'workflow_assets')

        workflow_dirs = app_settings.get('workflow_dirs', {})
        archive_dir_name = workflow_dirs.get('DIR_00_ARCHIVED', '00_materials_archived')
        to_process_dir_name = workflow_dirs.get('DIR_01_TO_PROCESS', '01_materials_to_process')

        recovered_count = 0

        for creative in failed_creatives:
            try:
                # 获取主体信息
                principal = db.get(Principal, creative.principal_id)
                if not principal:
                    logger.warning(f"素材 {creative.id} 缺少主体信息，跳过。")
                    continue

                filename = os.path.basename(creative.file_path)

                # 检查文件在归档文件夹中是否存在
                archive_dir = os.path.join(base_workflow_dir, archive_dir_name, principal.name)
                archived_file_path = os.path.join(archive_dir, filename)

                # 检查文件在各个位置是否存在
                original_exists = os.path.exists(creative.file_path)
                archived_exists = os.path.exists(archived_file_path)

                # 准备目标路径（处理文件夹）
                to_process_dir = os.path.join(base_workflow_dir, to_process_dir_name, principal.name)
                os.makedirs(to_process_dir, exist_ok=True)
                target_path = os.path.join(to_process_dir, filename)

                # 确定源文件位置和操作类型
                if original_exists and creative.file_path == target_path:
                    # 文件已经在正确位置，只需重置状态
                    logger.info(f"文件 {filename} 已在处理文件夹中，仅重置状态")
                    creative.status = 'pending_grouping'
                    creative.video_id = None
                    creative.material_id_qc = None
                    creative.uploaded_to_account_id = None

                elif original_exists and creative.file_path != target_path:
                    # 文件在原位置但不在处理文件夹，需要移动
                    if os.path.exists(target_path):
                        logger.warning(f"目标位置已存在文件 {filename}，删除原文件并重置状态")
                        os.remove(creative.file_path)
                    else:
                        logger.info(f"移动文件 {filename} 到处理文件夹")
                        shutil.move(creative.file_path, target_path)

                    # 重置素材状态和路径
                    creative.status = 'pending_grouping'
                    creative.file_path = target_path
                    creative.video_id = None
                    creative.material_id_qc = None
                    creative.uploaded_to_account_id = None

                elif archived_exists:
                    # 文件在归档文件夹，需要移回处理文件夹
                    if os.path.exists(target_path):
                        logger.warning(f"目标位置已存在文件 {filename}，删除归档文件并重置状态")
                        os.remove(archived_file_path)
                    else:
                        logger.info(f"从归档恢复文件 {filename} 到处理文件夹")
                        shutil.move(archived_file_path, target_path)

                    # 重置素材状态和路径
                    creative.status = 'pending_grouping'
                    creative.file_path = target_path
                    creative.video_id = None
                    creative.material_id_qc = None
                    creative.uploaded_to_account_id = None

                else:
                    # 文件在两个位置都不存在
                    logger.warning(f"素材文件 {filename} 在原位置和归档位置都不存在，跳过恢复。")
                    continue

                recovered_count += 1
                logger.success(f"✅ 素材 {filename} 已恢复到处理队列，状态 -> pending_grouping")

            except Exception as e:
                logger.error(f"恢复素材 {creative.id} 时出错: {e}", exc_info=True)
                continue

        # 提交所有更改
        db.commit()
        logger.success(f"--- [失败素材恢复] 完成，共恢复 {recovered_count} 个素材 ---")

    except Exception as e:
        logger.error(f"[失败素材恢复] 发生严重错误: {e}", exc_info=True)
        db.rollback()


def handle_file_ingestion(db: Session, app_settings: Dict[str, Any]):
    """
    [Producer V2] 扫描文件，计算哈希，并将素材信息存入数据库，状态为 'pending_grouping'。
    """
    logger.info("--- [工作流1A-Producer] 开始：扫描文件并入库 ---")
    try:
        base_workflow_dir = app_settings.get('custom_workflow_assets_dir')
        if not base_workflow_dir:
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            base_workflow_dir = os.path.join(project_root, 'workflow_assets')

        to_process_dir_name = app_settings.get('workflow_dirs', {}).get('DIR_01_TO_PROCESS', '01_materials_to_process')
        principal_dir_base = os.path.join(base_workflow_dir, to_process_dir_name)
        
        if not os.path.exists(principal_dir_base):
            logger.warning(f"入库文件夹 '{principal_dir_base}' 不存在，跳过。")
            return

        # 使用 joinedload 预加载关联数据，避免 N+1 查询
        all_principals = db.query(Principal).options(
            joinedload(Principal.ad_accounts)
        ).all()
        for principal in all_principals:
            principal_dir = os.path.join(principal_dir_base, principal.name)
            if not os.path.isdir(principal_dir): continue

            files_to_process = [os.path.join(root, f) for root, _, files in os.walk(principal_dir) for f in files if any(f.lower().endswith(ext) for ext in ['.mp4', '.mov', '.avi', '.mkv'])]
            if not files_to_process: continue

            ingested_count = 0
            for file_path in files_to_process:
                try:
                    try:
                        # 标准化文件路径
                        normalized_path = os.path.normpath(file_path)
                        if not os.path.exists(normalized_path):
                            logger.warning(f"跳过不存在的文件: {normalized_path}")
                            continue

                        file_hash = get_file_md5(normalized_path)
                        logger.debug(f"文件入库 - {os.path.basename(normalized_path)}: MD5={file_hash[:8]}...")
                    except (FileNotFoundError, IOError, ValueError) as e:
                        logger.warning(f"跳过文件 {file_path}: {e}")
                        continue

                    # 先检查是否存在，以判断是“新创建”还是“已存在”
                    existing_creative = db.query(LocalCreative).filter_by(file_hash=file_hash).first()

                    # 调用函数，传入file_path以便设置filename
                    creative = find_or_create_local_creative(db, principal.id, file_hash, file_path)

                    # 如果之前不存在，说明是新创建的
                    if not existing_creative:
                        creative.status = 'pending_grouping'
                        ingested_count += 1
                        logger.info(f"新素材入库: '{os.path.basename(file_path)}', 状态 -> pending_grouping")
                    # 如果已存在，并且状态适合重新入库
                    elif creative.status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:
                        creative.status = 'pending_grouping'
                        creative.file_path = file_path # 确保文件路径是最新的
                        creative.filename = os.path.basename(file_path) # 确保文件名是最新的
                        ingested_count += 1
                        logger.info(f"重新入库素材: '{os.path.basename(file_path)}', 状态 -> pending_grouping")
                    
                    db.commit()
                except Exception as e:
                    logger.error(f"处理单个文件入库时出错 (文件: {file_path}): {e}", exc_info=True)
                    db.rollback()
            
            if ingested_count > 0:
                logger.success(f"主体 '{principal.name}' 共入库 {ingested_count} 个新素材。")

    except Exception as e:
        logger.error(f"[工作流1A-Producer] 发生严重错误: {e}", exc_info=True)


def group_and_dispatch_uploads(db: Session, app_settings: Dict[str, Any]):
    """
    [Grouping Dispatcher] 聚合等待分组的素材，并按需派发上传任务。
    """
    global last_used_account_index
    logger.info("--- [工作流1B-Dispatcher] 开始：聚合素材并派发上传组 ---")
    try:
        all_principals = db.query(Principal).options(joinedload(Principal.ad_accounts)).all()
        
        for principal in all_principals:
            # 1. 检查健康账户
            client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=principal.id)
            healthy_accounts = [acc for acc in principal.ad_accounts if acc.account_type == 'TEST' and acc.status == 'active' and is_account_healthy(db, client, acc)]
            if not healthy_accounts:
                logger.warning(f"主体 '{principal.name}' 无健康测试账户，无法派发上传任务。")
                continue

            # 2. 获取该主体下所有待分组的素材
            pending_creatives = db.query(LocalCreative).filter(
                LocalCreative.principal_id == principal.id,
                LocalCreative.status == 'pending_grouping'
            ).all()

            if not pending_creatives:
                logger.info(f"主体 '{principal.name}' 的素材蓄水池中没有待处理素材。")
                continue

            # 3. 检查是否满足成组条件 - 使用灵活分组
            grouping_config = get_flexible_grouping_config(app_settings)
            min_required = grouping_config['min_creative_count']
            
            logger.info(f"主体 '{principal.name}' 蓄水池中有 {len(pending_creatives)} 个素材，灵活分组最少需要 {min_required} 个。")

            # 检查超时情况
            if len(pending_creatives) < min_required:
                # 检查是否有超时的视频需要强制处理
                if should_force_create_plan(pending_creatives, 
                                          grouping_config['force_create_threshold'], 
                                          grouping_config['timeout_hours']):
                    logger.warning(f"主体 '{principal.name}' 素材数量不足但有超时视频，强制创建计划")
                else:
                    logger.info(f"主体 '{principal.name}' 素材数量不足且无超时，等待下一轮调度。")
                    continue

            # 4. 成组并派发 - 使用灵活分组
            max_group_size = grouping_config['max_creative_count']
            num_groups = len(pending_creatives) // max_group_size
            logger.success(f"主体 '{principal.name}' 素材充足，可以组成 {num_groups} 个上传组。")

            creatives_to_dispatch = pending_creatives[:num_groups * max_group_size]

            for i in range(0, len(creatives_to_dispatch), max_group_size):
                chunk = creatives_to_dispatch[i:i + max_group_size]

                # 为这一整组选择一个账户
                current_index = last_used_account_index[principal.id]
                account_for_upload = healthy_accounts[current_index % len(healthy_accounts)]
                last_used_account_index[principal.id] = (current_index + 1)
                logger.info(f"为素材组 {i//max_group_size + 1} 选择账户: '{account_for_upload.name}'")

                # 派发这一组的所有上传任务
                for creative in chunk:
                    try:
                        # 更新状态为 'processing'，实现锁定
                        creative.status = 'processing'
                        db.commit()

                        tasks.upload_single_video.delay(
                            local_creative_id=creative.id,
                            account_id=account_for_upload.id,
                            file_path=creative.file_path,
                            principal_name=principal.name
                        )
                        logger.info(f"  -> 已为 '{os.path.basename(creative.file_path)}' 派发上传任务。")
                    except Exception as e:
                        logger.error(f"派发单个任务时出错 (Creative ID: {creative.id}): {e}", exc_info=True)
                        db.rollback() # 回滚状态变更

    except Exception as e:
        logger.error(f"[工作流1B-Dispatcher] 发生严重错误: {str(e)}", exc_info=True)

def handle_plan_creation(db: Session, app_settings: Dict[str, Any]):
    logger.info("--- [工作流1.8] 开始：为已就绪素材创建计划 (V3 - 终极版) ---")
    try:
        # --- 阶段一：健康检查与任务回滚 ---
        logger.info("--- [阶段1/2] 开始健康检查与任务回滚 ---")

        # [V2025.07.24 - 业务逻辑修正版] 移除CREATING_PLAN状态处理
        # 计划创建是同步操作，不需要中间状态
        from datetime import datetime, timedelta
        from qianchuan_aw.utils.workflow_status import WorkflowStatus

        # 查询所有待创建计划的素材
        # 只查询uploaded_pending_plan状态的素材
        creatives_to_check = db.query(PlatformCreative).join(LocalCreative).filter(
            LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value
        ).options(
            joinedload(PlatformCreative.account).joinedload(AdAccount.principal),
            joinedload(PlatformCreative.local_creative)
        ).all()

        if not creatives_to_check:
            logger.info("--- [阶段1/2] 没有待处理的素材，阶段结束。 ---")
            return

        grouped_by_account = defaultdict(list)
        for pc in creatives_to_check:
            grouped_by_account[pc.account].append(pc)
        
        for account, creatives in grouped_by_account.items():
            client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=account.principal_id)
            if not is_account_healthy(db, client, account):
                rollback_and_reset_creatives(db, creatives, f"账户 '{account.name}' 不健康", app_settings)
        
        logger.info("--- [阶段1/2] 健康检查与任务回滚完成。 ---")

        # --- 阶段二：重复测试检查与计划创建 ---
        logger.info("--- [阶段2/3] 开始重复测试检查 ---")

        # 查询所有待创建计划的素材
        # 只查询uploaded_pending_plan状态的素材
        candidate_creatives = db.query(PlatformCreative).join(LocalCreative).filter(
            LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value
        ).options(
            joinedload(PlatformCreative.account).joinedload(AdAccount.principal),
            joinedload(PlatformCreative.local_creative)
        ).all()

        logger.info(f"--- [追踪点A] 找到 {len(candidate_creatives)} 个状态为 'uploaded_pending_plan' 的素材候选。")

        # [V-Fix-2-Enhanced] 强化重复测试检查逻辑 - 绝对防止重复测试计划
        final_creatives_to_build = []
        skipped_creatives = []

        for pc in candidate_creatives:
            local_creative = pc.local_creative

            try:
                # [V-Fix-3] 修复事务冲突 - 不使用嵌套事务
                # 锁定本地素材记录
                locked_creative = db.query(LocalCreative).filter(
                    LocalCreative.id == local_creative.id
                ).with_for_update().first()

                if not locked_creative:
                    logger.warning(f"无法锁定素材 {local_creative.id}，跳过")
                    continue

                # 再次检查状态
                if locked_creative.status != WorkflowStatus.UPLOADED_PENDING_PLAN.value:
                    logger.info(f"素材 {os.path.basename(locked_creative.file_path)} 状态已变更为 {locked_creative.status}，跳过")
                    continue

                # [增强重复检查] 基于file_hash的严格唯一性检查 - 业务规则铁律版
                # 🛡️ 素材唯一性测试铁律：同一file_hash在测试账户中只能创建一个计划
                try:
                    existing_campaigns_by_hash = db.query(Campaign).join(
                        campaign_platform_creative_association,
                        Campaign.id == campaign_platform_creative_association.c.campaign_id
                    ).join(
                        PlatformCreative,
                        campaign_platform_creative_association.c.platform_creative_id == PlatformCreative.id
                    ).join(
                        LocalCreative,
                        PlatformCreative.local_creative_id == LocalCreative.id
                    ).join(
                        AdAccount,
                        Campaign.account_id == AdAccount.id
                    ).filter(
                        LocalCreative.file_hash == locked_creative.file_hash,
                        AdAccount.account_type == 'TEST'  # 🔧 修复：只检查测试账户
                    ).count()

                    if existing_campaigns_by_hash > 0:
                        # 基于file_hash发现重复计划，这是最严格的检查
                        skipped_creatives.append(pc)
                        logger.warning(f"🛡️ [业务规则铁律] 素材 {os.path.basename(locked_creative.file_hash)} (hash: {locked_creative.file_hash[:8]}...) 已在测试账户中有计划，严格跳过重复创建")

                        # 将状态更新为已测试
                        locked_creative.status = WorkflowStatus.ALREADY_TESTED.value
                        db.commit()
                        continue

                except Exception as e:
                    # 🚨 业务规则铁律：重复检查失败时必须阻止创建，不能继续处理
                    logger.error(f"🛡️ [业务规则铁律] 素材唯一性检查失败，为保证铁律遵循，跳过此素材: {str(e)}")
                    skipped_creatives.append(pc)
                    continue  # 🔧 修复：检查失败时跳过，不继续创建
                
                # [原有检查] 通过多种方式检查是否已有测试计划
                # 方法1: 通过关联表检查
                existing_campaigns_via_association = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).count()

                # 方法2: 直接检查该素材的所有平台素材是否有关联的计划
                platform_creatives_for_this_local = db.query(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).all()

                has_existing_campaigns = False
                for pc_check in platform_creatives_for_this_local:
                    if pc_check.campaigns:  # 如果有关联的计划
                        has_existing_campaigns = True
                        break

                if existing_campaigns_via_association > 0 or has_existing_campaigns:
                    # 该素材已经创建过测试计划，跳过
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 已创建过测试计划，跳过重复测试")

                    # 将状态更新为特殊状态，表示已测试过
                    locked_creative.status = WorkflowStatus.ALREADY_TESTED.value
                    db.commit()  # 立即提交状态更新

                else:
                    # [最终检查] 在创建前再次检查，防止竞态条件
                    final_check = db.query(Campaign).join(
                        campaign_platform_creative_association
                    ).join(PlatformCreative).join(LocalCreative).filter(
                        LocalCreative.file_hash == locked_creative.file_hash
                    ).count()
                    
                    if final_check > 0:
                        logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 在最终检查时发现已有计划，跳过")
                        locked_creative.status = WorkflowStatus.ALREADY_TESTED.value
                        db.commit()
                        continue

                    # 该素材从未创建过测试计划，可以进行测试
                    # 不设置中间状态，直接添加到创建列表（同步创建）
                    final_creatives_to_build.append(pc)
                    logger.info(f"素材 {os.path.basename(locked_creative.file_path)} 通过所有重复检查，准备创建计划")

            except Exception as e:
                logger.error(f"检查素材 {local_creative.id} 重复测试时出错: {e}", exc_info=True)
                db.rollback()
                continue

        # 提交状态更新
        if skipped_creatives:
            db.commit()
            logger.info(f"--- [重复检查] 跳过了 {len(skipped_creatives)} 个已测试过的素材")

        logger.info(f"--- [追踪点B] 经过重复检查后，{len(final_creatives_to_build)} 个素材可以创建测试计划。")

        # --- 阶段三：为通过检查的素材创建计划 ---
        logger.info("--- [阶段3/3] 开始为通过检查的素材创建计划 ---")
        if not final_creatives_to_build:
            logger.info("--- [逻辑中断] 没有待处理的素材，计划创建工作流结束。 ---")
            return

        final_grouped_by_account = defaultdict(list)
        for pc in final_creatives_to_build:
            if pc.account.status == 'active':
                final_grouped_by_account[pc.account].append(pc)

        for account, creatives in final_grouped_by_account.items():
            logger.info(f"--- 账户 '{account.name}' 发现 {len(creatives)} 个待创建计划的素材 ---")

            workflow_key = 'test_workflow' if account.account_type == 'TEST' else 'manual_workflow'
            workflow_config = app_settings['plan_creation_defaults'].get(workflow_key, {})
            required_creative_count = workflow_config.get('creative_count', 9 if account.account_type == 'TEST' else 3)
            logger.info(f"--- [追踪点B] 账户 '{account.name}' (类型: {account.account_type}) 需要 {required_creative_count} 个素材来创建计划。")
 
            # 使用灵活分组检查
            grouping_config = get_flexible_grouping_config(app_settings)
            min_required = grouping_config['min_creative_count']
            
            if len(creatives) < min_required:
                # 检查是否应该强制创建
                if should_force_create_plan(creatives, 
                                          grouping_config['force_create_threshold'], 
                                          grouping_config['timeout_hours']):
                    logger.warning(f"--- [强制创建] 账户 '{account.name}' 素材数量 ({len(creatives)}) 不足但有超时，强制创建计划。")
                else:
                    logger.warning(f"--- [逻辑中断] 账户 '{account.name}' 的素材数量 ({len(creatives)}) 不足 {min_required}，本轮不为该账户创建计划。")
                    continue

            chunk_size = required_creative_count
            for i in range(0, len(creatives), chunk_size):
                chunk = creatives[i:i + chunk_size]
                if len(chunk) < required_creative_count: break
                
                # [临时修正] 注释掉速率限制判断，后续如需恢复请取消注释
                # if not check_rate_limit(account.id, app_settings['workflow']['hourly_plan_creation_rate_limit'], app_settings):
                #     logger.warning(f"账户 '{account.name}' 已达速率限制，跳过创建。")
                #     break
                # TODO: 后续如需恢复速率限制，请取消上述注释
                try:
                    bid_type = workflow_config.get('bid_type', 'DEAL')
                    cpa_bid = workflow_config.get('cpa_bid') if bid_type == 'DEAL' else None
                    budget = workflow_config.get('budget', 300.0)
                    
                    logger.info(f"为账户类型 '{account.account_type}' 的计划使用配置参数 -> 预算: {budget}, CPA出价: {cpa_bid}")

                    # [临时修正] 注释掉全局唯一性检查相关导入和调用，后续如需恢复请补充 check_test_video_global_uniqueness 实现
                    # from qianchuan_aw.workflows.scheduler import check_test_video_global_uniqueness  # TODO: 补充唯一性校验函数实现

                    # 在 create_ad_plan 调用前后，原有涉及 check_test_video_global_uniqueness 的逻辑请根据业务需求补充
                    # TODO: 后续如需严格防止重复测试计划，请实现 check_test_video_global_uniqueness 并在此处调用
                    result_campaign = create_ad_plan(
                        db=db,
                        principal=account.principal,
                        account=account,
                        platform_creatives=chunk,
                        campaign_scene=workflow_config.get('campaign_scene', 'DAILY_SALE'),
                        bid_type=bid_type,
                        is_lab_ad=workflow_config.get('is_lab_ad', False),
                        budget=budget,
                        cpa_bid=cpa_bid,
                        roi_goal=None,
                        app_settings=app_settings
                    )
                    if result_campaign:
                        record_plan_creation(account.id, app_settings)
                        logger.info(f"计划创建成功，等待 {app_settings['robustness']['plan_creation_interval']} 秒...")
                        time.sleep(app_settings['robustness']['plan_creation_interval'])
                    else:
                        logger.warning(f"账户 {account.name} 的计划创建函数未返回成功结果，跳过等待。")
                        logger.critical(f"由于计划创建失败，将终止为账户 '{account.name}' 处理本轮剩余的素材组。")
                        break
                except Exception as e:
                    logger.error(f"为账户 {account.name} 创建计划时发生未知错误: {e}", exc_info=True)
                    break
    
    except Exception as e:
        # 安全的错误日志记录，避免字符串格式化问题
        error_msg = str(e).replace("'", "").replace("{", "").replace("}", "")
        logger.error(f"[工作流1.8] 发生严重错误: {error_msg}", exc_info=True)



def handle_timeout_campaigns_comprehensive(db: Session, app_settings: Dict[str, Any]):
    """全面检查和处理超时的提审计划"""
    logger.info("--- [工作流2.1] 开始：全面超时检查 ---")
    try:
        appeal_max_hours = app_settings.get('robustness', {}).get('appeal_max_duration_hours', 10)
        timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=appeal_max_hours)
        
        # 查找所有超时的计划（不限制状态）
        timeout_campaigns = db.query(Campaign).options(
            joinedload(Campaign.account)
        ).filter(
            Campaign.first_appeal_at.isnot(None),
            Campaign.first_appeal_at < timeout_threshold,
            Campaign.status.notin_(['APPEAL_TIMEOUT', 'MONITORING', 'APPROVED', 'REJECTED'])
        ).join(AdAccount).filter(
            AdAccount.account_type == 'TEST'
        ).all()
        
        if not timeout_campaigns:
            logger.info("没有发现超时的提审计划")
            return
        
        logger.warning(f"发现 {len(timeout_campaigns)} 个超时的提审计划")
        
        for campaign in timeout_campaigns:
            hours_overdue = (datetime.now(timezone.utc) - campaign.first_appeal_at).total_seconds() / 3600
            logger.warning(f"计划 {campaign.campaign_id_qc} 首次提审已超过 {hours_overdue:.1f} 小时，标记为超时")
            
            campaign.status = 'APPEAL_TIMEOUT'
            campaign.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        logger.info(f"已将 {len(timeout_campaigns)} 个超时计划标记为 APPEAL_TIMEOUT")
        
    except Exception as e:
        logger.error(f"全面超时检查失败: {e}", exc_info=True)
        db.rollback()


def handle_plan_submission(db: Session, app_settings: Dict[str, Any]):
    """处理新创建计划的提审 - 使用优化的批量提审服务"""
    logger.info("--- [工作流1.5] 开始：提交新创建的计划（优化版本）---")

    try:
        # 查找状态为AUDITING但未提审的计划（确保不重复提审）
        plans_to_submit = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal)
        ).filter(
            Campaign.status == 'AUDITING',
            # 确保未提审过：appeal_status为空且first_appeal_at为空
            Campaign.appeal_status.is_(None),
            Campaign.first_appeal_at.is_(None),
            # 额外保险：appeal_attempt_count为空或为0
            or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0)
        ).all()

        if not plans_to_submit:
            logger.info("没有找到需要提审的新计划")
            return

        logger.info(f"发现 {len(plans_to_submit)} 个新计划需要提审")

        # 按账户分组处理
        plans_by_account = defaultdict(list)
        for plan in plans_to_submit:
            plans_by_account[plan.account].append(plan)

        submission_count = 0

        # 转换为优化服务需要的格式
        plans_data = []
        for plan in plans_to_submit:
            # 检查账户类型，如果是正式投放账户且配置不允许提审，则跳过
            if plan.account.account_type == 'DELIVERY' and not app_settings.get('appeal_strategy', {}).get('appeal_for_prod_plans', False):
                logger.info(f"计划 {plan.campaign_id_qc} 属于正式投放账户，根据配置跳过自动提审")
                plan.appeal_status = 'skipped_prod_account'
                plan.appeal_error_message = '正式投放账户，跳过自动提审'
                continue

            plans_data.append({
                'campaign_id': plan.campaign_id_qc,
                'principal_name': plan.account.principal.name,
                'account_id': plan.account.account_id_qc
            })

        if not plans_data:
            logger.info("所有计划都被跳过（正式投放账户）")
            db.commit()
            return

        # 使用优化的批量提审服务
        from qianchuan_aw.services.production_appeal_service import create_production_appeal_service

        appeal_service = create_production_appeal_service(app_settings)

        # 获取性能统计
        stats = appeal_service.get_performance_statistics(plans_data)
        logger.info(f"🚀 优化效果预期：资源节约 {stats['resource_savings_percent']:.1f}%，时间节约 {stats['time_savings_percent']:.1f}%")

        # 执行优化的批量提审
        results = appeal_service.batch_appeal_all_plans(plans_data)

        # 更新数据库
        updated_count = appeal_service.update_database_with_results(db, results)

        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        submission_count = success_count

        # 提交数据库更改
        db.commit()

        logger.success(f"✅ 优化批量提审完成：成功 {success_count}/{total_count}，数据库更新 {updated_count} 个")

    except Exception as e:
        logger.error(f"❌ 计划提审处理失败: {e}")
        db.rollback()
        raise
    finally:
        logger.info("--- [工作流1.5] 结束：提交新创建的计划（优化版本）---")


def handle_plans_awaiting_appeal(db: Session, app_settings: Dict[str, Any]):
    logger.info("--- [工作流2] 开始：检查并提审计划 ---")
    try:
        # 修复：查找正确的申诉状态 - 查找appeal_pending状态而不是AUDITING状态
        plans_to_check = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal)
        ).filter(Campaign.appeal_status == 'appeal_pending').all()

        if not plans_to_check:
            logger.info("没有找到待申诉的计划")
            return

        plans_by_account = defaultdict(list)
        for plan in plans_to_check:
            plans_by_account[plan.account].append(plan)

        for account, plans_in_account in plans_by_account.items():
            principal = account.principal
            client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=principal.id)
            plan_ids = [p.campaign_id_qc for p in plans_in_account]
            
            end_time = datetime.now()
            start_time = end_time - timedelta(days=2)
            filtering = {"ids": plan_ids, "create_time": {"start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'), "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S')}}
            online_plans = client.get_ad_plan_list(advertiser_id=account.account_id_qc, filtering=filtering)
            if not online_plans:
                logger.warning(f"账户 '{account.name}' 的 {len(plan_ids)} 个待查计划未返回任何信息。")
                continue

            online_status_map = {str(p.get("ad_id")): p.get("status") for p in online_plans}
            
            plans_needing_verification = []
            for plan in plans_in_account:
                online_status = online_status_map.get(str(plan.campaign_id_qc))
                if not online_status:
                    plan.status = 'DELETED'; db.commit()
                    continue
                if online_status == 'AUDIT': continue

                if account.account_type == 'DELIVERY' and not app_settings['appeal_strategy']['appeal_for_prod_plans']:
                    logger.info(f"计划 {plan.campaign_id_qc} 属于正式投放账户，根据配置不进行自动提审，将其标记为完成。")
                    _harvest_and_complete_plan(db, plan, principal, app_settings)
                    continue
                
                if plan.first_appeal_at and (datetime.now(timezone.utc) - plan.first_appeal_at) > timedelta(hours=app_settings['robustness']['appeal_max_duration_hours']):
                    logger.warning(f"计划 {plan.campaign_id_qc} 首次提审已超过 {app_settings['robustness']['appeal_max_duration_hours']} 小时，判定为超时。")
                    plan.status = 'APPEAL_TIMEOUT'
                    db.commit()
                    continue

                # 🛡️ 申诉提审一次性原则：检查是否已经申诉过
                if plan.appeal_attempt_count and plan.appeal_attempt_count > 0:
                    logger.warning(f"🛡️ [业务规则铁律] 计划 {plan.campaign_id_qc} 已申诉过 {plan.appeal_attempt_count} 次，严格遵循一次性原则，跳过")
                    continue

                logger.info(f"计划 {plan.campaign_id_qc} 已脱离初审 (当前状态: {online_status})，准备首次提审...")

                appeal_successful = False

                try:
                    with CopilotSession(principal.name, account.account_id_qc, app_settings) as session:
                        appeal_successful = session.appeal_via_text_command(plan.campaign_id_qc)
                except Exception as e:
                    logger.error(f"为计划 {plan.campaign_id_qc} 执行文本指令提审时发生严重错误: {e}", exc_info=True)
                    appeal_successful = False

                if not appeal_successful:
                    logger.warning(f"计划 {plan.campaign_id_qc} 文本指令提审失败，降级到UI自动化模式...")
                    try:
                        from qianchuan_aw.services.appeal_browser_service import perform_appeal_via_browser
                        browser_success, _ = perform_appeal_via_browser(
                            principal_name=principal.name,
                            account_id_qc=account.account_id_qc,
                            plan_id=plan.campaign_id_qc,
                            app_settings=app_settings
                        )
                        if browser_success:
                            appeal_successful = True
                    except Exception as e:
                        logger.error(f"为计划 {plan.campaign_id_qc} 执行浏览器UI提审时发生严重错误: {e}", exc_info=True)

                if appeal_successful:
                    # 🔧 修复：更新申诉计数和时间
                    if not plan.first_appeal_at:
                        plan.first_appeal_at = datetime.now(timezone.utc)

                    # 🛡️ 业务规则铁律：更新申诉尝试次数
                    plan.appeal_attempt_count = (plan.appeal_attempt_count or 0) + 1
                    plan.appeal_started_at = datetime.now(timezone.utc)

                    db.commit()
                    logger.success(f"🛡️ [业务规则铁律] 计划 {plan.campaign_id_qc} 首次提审成功，申诉计数: {plan.appeal_attempt_count}")
                    plans_needing_verification.append(plan)
                else:
                    logger.error(f"计划 {plan.campaign_id_qc} 所有提审模式均失败，将等待下轮调度重试。")

            if not plans_needing_verification: continue

            logger.info(f"账户 {account.name} 有 {len(plans_needing_verification)} 个计划需要事实核查，准备进入会话...")
            try:
                with CopilotSession(principal.name, account.account_id_qc, app_settings) as session:
                    for plan in plans_needing_verification:
                        logger.info(f"计划 {plan.campaign_id_qc} 初步提审成功，正在启动事实核查...")
                        verification_result = session.query_plan(plan.campaign_id_qc)
                        
                        if verification_result == 'APPEALING':
                            plan.status = 'MONITORING'; plan.last_appeal_at = datetime.now(timezone.utc); db.commit()
                            logger.success(f"✅ 事实核查通过！计划 {plan.campaign_id_qc} 已确认进入监控状态。")
                        elif verification_result in ['APPEAL_SUCCESS', 'APPEAL_FAILED', 'ALREADY_APPROVED']:
                            if verification_result == 'ALREADY_APPROVED':
                                logger.success(f"✅ 事实核查发现计划 {plan.campaign_id_qc} 已是“非拒绝状态”，视为通过，直接收割！")
                            else:
                                logger.warning(f"⚠️ 事实核查发现计划 {plan.campaign_id_qc} 已有最终结果 ({verification_result})，直接收割。")
                            _harvest_and_complete_plan(db, plan, principal, app_settings)
                        elif verification_result == 'APPEAL_SUBMITTED':
                            plan.status = 'MONITORING'; plan.last_appeal_at = datetime.now(timezone.utc); db.commit()
                            logger.success(f"✅ 事实核查确认计划 {plan.campaign_id_qc} 申诉已提交，进入监控状态。")
                        elif verification_result == 'NO_RECORD':
                            logger.error(f"❌ 事实核查失败！计划 {plan.campaign_id_qc} 在后台无申诉记录，立即强制使用浏览器再次提审...")
                            from qianchuan_aw.services.appeal_browser_service import perform_appeal_via_browser
                            browser_success, _ = perform_appeal_via_browser(principal.name, account.account_id_qc, plan.campaign_id_qc, app_settings)
                            if browser_success: logger.success(f"强制浏览器提审计划 {plan.campaign_id_qc} 成功，等待下轮核查。")
                            else: logger.error(f"强制浏览器提审计划 {plan.campaign_id_qc} 仍然失败。")
                        else: # UNKNOWN or SYSTEM_ERROR
                            logger.error(f"❌ 事实核查过程本身发生错误({verification_result})，计划 {plan.campaign_id_qc} 等待下轮重试。")
            except Exception as e_session:
                logger.error(f"为账户 {account.name} 执行事实核查会话时发生严重错误: {e_session}", exc_info=True)

    except Exception as e:
        logger.error(f"[工作流2] 发生严重错误: {e}", exc_info=True)
        db.rollback()

LOG_CACHE = {}

def _harvest_materials_from_plan(db: Session, plan: Campaign, principal: Principal, app_settings: Dict[str, Any]):
    logger.debug(f"开始对计划 {plan.campaign_id_qc} 进行持续性素材收割检查...")

    # 🛡️ 收割动作精确控制：检查申诉状态，防止过早收割
    if plan.appeal_status in ['appeal_submitted', 'appeal_pending'] and plan.appeal_completed_at is None:
        logger.info(f"🛡️ [业务规则铁律] 计划 {plan.campaign_id_qc} 仍在申诉中 (状态: {plan.appeal_status})，跳过收割动作")
        return

    try:
        client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=principal.id)
        online_materials = client.get_materials_in_ad(advertiser_id=plan.account.account_id_qc, ad_id=plan.campaign_id_qc)
        
        if online_materials:
            for material_info in online_materials:
                material_id = material_info.get("material_info", {}).get("video_material", {}).get("material_id")
                if not material_id: continue
                
                pc = db.query(PlatformCreative).filter(PlatformCreative.material_id_qc == str(material_id)).first()
                if not pc: continue

                audit_status = material_info.get("audit_status")
                if audit_status == 'PASS':
                    if pc.local_creative.status != 'approved':
                        _add_to_approved_library(db, pc, app_settings)
                elif audit_status in ['REJECT', 'AUDIT_REJECT']:
                    if pc.local_creative.status != 'rejected':
                        logger.warning(f"素材 {material_id} 在计划 {plan.campaign_id_qc} 中被拒绝 (状态: {audit_status})。")
                        pc.local_creative.status = 'rejected'
                        db.commit()
    except Exception as e:
        logger.error(f"为计划 {plan.campaign_id_qc} 执行持续收割时出错: {e}", exc_info=True)


def _harvest_and_complete_plan(db: Session, plan: Campaign, principal: Principal, app_settings: Dict[str, Any]):
    logger.info(f"对计划 {plan.campaign_id_qc} 执行最终收割并完结...")
    _harvest_materials_from_plan(db, plan, principal, app_settings)
    
    if plan.account.account_type == 'DELIVERY' and not app_settings['appeal_strategy'].get('appeal_for_prod_plans', False):
        logger.info(f"计划 {plan.campaign_id_qc} 属于正式投放账户且配置为不提审，跳过素材归档。")
    else:
        logger.info(f"开始为完结的计划 {plan.campaign_id_qc} 清理被拒绝的素材文件...")
        for pc in plan.platform_creatives:
            if pc.local_creative and pc.local_creative.status == 'rejected':
                base_workflow_dir = app_settings.get('custom_workflow_assets_dir')
                if not base_workflow_dir:
                    current_file_dir = os.path.dirname(os.path.abspath(__file__))
                    project_root = os.path.abspath(os.path.join(current_file_dir, '..', '..', '..'))
                    base_workflow_dir = os.path.join(project_root, 'workflow_assets')

                workflow_dirs_config = app_settings.get('workflow_dirs', {})
                archive_dir_name = workflow_dirs_config.get('DIR_00_ARCHIVED', '00_materials_archived')
                archived_file_path = os.path.join(base_workflow_dir, archive_dir_name, principal.name, os.path.basename(pc.local_creative.file_path))
                if os.path.exists(archived_file_path):
                    try:
                        os.remove(archived_file_path)
                        logger.success(f"已成功删除被拒绝的素材归档文件: {archived_file_path}")
                    except OSError as e:
                        logger.error(f"删除被拒绝的素材文件 {archived_file_path} 时出错: {e}")
                else:
                    logger.warning(f"尝试删除被拒绝的素材文件，但文件不存在: {archived_file_path}")

    plan.status = 'COMPLETED'
    db.commit()
    logger.success(f"计划 {plan.campaign_id_qc} 素材收割与清理完毕，监控完成。")


def handle_independent_material_harvest(db: Session, app_settings: Dict[str, Any]):
    """
    [V59.0] 独立素材收割工作流
    不依赖申诉结果，定期扫描所有计划中的素材状态并收割通过的素材
    """
    logger.info("--- [工作流3A] 开始：独立素材收割 ---")
    try:
        from qianchuan_aw.workflows.independent_material_harvest import scan_and_harvest_materials
        scan_and_harvest_materials(db, app_settings)
    except Exception as e:
        logger.error(f"独立素材收割工作流发生错误: {e}", exc_info=True)
        db.rollback()

def handle_monitoring_of_materials(db: Session, app_settings: Dict[str, Any]):
    logger.info("--- [工作流3B] 开始：持续收割并监控申诉状态 ---")
    try:
        plans_to_monitor = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal)
        ).filter(Campaign.status == 'MONITORING').all()
        if not plans_to_monitor: return

        plans_by_account = defaultdict(list)
        for plan in plans_to_monitor:
            plans_by_account[plan.account].append(plan)

        for account, plans_in_account in plans_by_account.items():
            principal = account.principal
            
            for plan in plans_in_account:
                _harvest_materials_from_plan(db, plan, principal, app_settings)

            log_key = f"account_{account.id}_appeal_status_check"
            last_check_time = LOG_CACHE.get(log_key, 0)
            if time.time() - last_check_time < app_settings['robustness']['monitoring_interval']:
                logger.info(f"账户 {account.name} 未到监控时间，跳过。")
                continue
            
            logger.info(f"账户 {account.name} 已超过监控间隔，开始批量查询智投星...")
            try:
                with CopilotSession(principal.name, account.account_id_qc, app_settings) as session:
                    for plan in plans_in_account:
                        appeal_status = session.query_plan(plan.campaign_id_qc)
                        
                        if appeal_status in ["APPEAL_SUCCESS", "APPEAL_FAILED", "ALREADY_APPROVED"]:
                            if appeal_status == "APPEAL_SUCCESS":
                                logger.success(f"计划 {plan.campaign_id_qc} 申诉成功！进入最终收割。")
                            elif appeal_status == "ALREADY_APPROVED":
                                logger.success(f"计划 {plan.campaign_id_qc} 监控发现其已是“非拒绝状态”，视为通过，进入最终收割！")
                            else:
                                logger.warning(f"计划 {plan.campaign_id_qc} 申诉失败。进入最终收割。")
                            _harvest_and_complete_plan(db, plan, principal, app_settings)

                        elif appeal_status == "APPEAL_SUBMITTED":
                            logger.info(f"计划 {plan.campaign_id_qc} 申诉已提交，继续监控等待结果。")
                        
                        elif appeal_status == 'NO_RECORD':
                            logger.error(f"❌ 监控发现计划 {plan.campaign_id_qc} 在后台无申诉记录，立即强制使用浏览器再次提审...")
                            from qianchuan_aw.services.appeal_browser_service import perform_appeal_via_browser
                            browser_success, _ = perform_appeal_via_browser(principal.name, account.account_id_qc, plan.campaign_id_qc, app_settings)
                            if browser_success:
                                logger.success(f"强制浏览器提审计划 {plan.campaign_id_qc} 成功，等待下轮核查。")
                                # 更新提审时间，确保账户隔离
                                plan.last_appeal_at = datetime.now(timezone.utc)
                                db.commit()
                                logger.info(f"[账户隔离] 已在广告户 {account.account_id_qc} 下完成计划 {plan.campaign_id_qc} 的重新提审")
                            else:
                                logger.error(f"强制浏览器提审计划 {plan.campaign_id_qc} 仍然失败。")

                        elif appeal_status == "APPEALING":
                            logger.info(f"计划 {plan.campaign_id_qc} 仍在申诉中，已完成本轮持续收割。")
                        
                        else: # UNKNOWN or SYSTEM_ERROR
                            logger.info(f"计划 {plan.campaign_id_qc} 智投星状态未知或系统异常({appeal_status})，已完成本轮持续收割。")
                        
                        logger.debug("智能等待5秒，防止请求过快...")
                        time.sleep(5) # 使用 time.sleep，gevent会自动处理
                
                LOG_CACHE[log_key] = time.time()

            except Exception as e:
                logger.error(f"处理账户 {account.name} 的监控时发生未知错误: {e}", exc_info=True)
        
    except Exception as e:
        logger.error(f"[工作流3] 发生严重错误: {str(e)}", exc_info=True)
        db.rollback()


def handle_violation_detection(db: Session, app_settings: Dict[str, Any]):
    """
    [V62.2] 独立的违规检测工作流：检查账户扣分、违规情况
    """
    logger.info("--- [工作流-违规检测] 开始：检查账户违规情况 ---")
    try:
        all_principals = db.query(Principal).options(joinedload(Principal.ad_accounts)).all()

        # 过滤需要检查的主体
        filtered_principals = []
        patrol_principals = app_settings['workflow'].get('comment_and_violation_patrol_principals', [])
        if patrol_principals:
            for p in all_principals:
                if p.name in patrol_principals:
                    filtered_principals.append(p)
                else:
                    logger.debug(f"主体 '{p.name}' 不在配置的巡检列表中，跳过违规检查。")
        else:
            filtered_principals = all_principals

        if not filtered_principals:
            logger.info("没有配置需要进行违规巡检的主体，或者数据库中没有匹配的主体。")
            return

        violation_found = False
        for principal in filtered_principals:
            client = QianchuanClient(
                app_id=app_settings['api_credentials']['app_id'],
                secret=app_settings['api_credentials']['secret'],
                principal_id=principal.id
            )

            for account in principal.ad_accounts:
                if account.status == 'deleted':
                    continue

                logger.debug(f"正在检查账户 {account.name} (ID: {account.account_id_qc}) 的违规情况...")

                try:
                    # 检查处罚信息
                    disposal_info = client.get_disposal_info(advertiser_id=account.account_id_qc)
                    if disposal_info and disposal_info.get("disposal_info_list"):
                        logger.warning(f"账户 {account.name} 发现处罚记录: {disposal_info}")
                        violation_found = True

                        # 更新账户状态
                        if account.status != 'deleted':
                            account.status = 'deleted'
                            db.commit()
                            logger.critical(f"账户 {account.name} 因处罚记录已标记为删除状态")

                    # 检查违规积分
                    end_date = datetime.now(timezone.utc)
                    start_date = end_date - timedelta(days=7)
                    violation_events = client.get_score_violation_event(
                        advertiser_id=account.account_id_qc,
                        start_time=start_date.strftime('%Y-%m-%d %H:%M:%S'),
                        end_time=end_date.strftime('%Y-%m-%d %H:%M:%S'),
                        filtering={"status": "VALID"}
                    )
                    if violation_events:
                        recent_violations = []
                        for event in violation_events:
                            if event.get("score", 0) > 0:  # 有扣分的违规
                                event_time = datetime.strptime(event["create_time"], '%Y-%m-%d %H:%M:%S')
                                if datetime.now() - event_time < timedelta(days=7):  # 最近7天的违规
                                    recent_violations.append(event)

                        if recent_violations:
                            logger.warning(f"账户 {account.name} 最近7天内有 {len(recent_violations)} 个违规记录")
                            violation_found = True

                            # 检查是否需要临时封禁
                            for event in recent_violations:
                                if (event.get("score") == 4 and
                                    "中度撞审" in event.get("reject_reason", "") and
                                    event.get("illegal_type") == "TWOTHREECLASS"):

                                    event_create_time = datetime.strptime(event["create_time"], '%Y-%m-%d %H:%M:%S')
                                    if datetime.now() - event_create_time < timedelta(days=3):
                                        account.status = 'temporarily_blocked'
                                        account.blocked_until = datetime.now(timezone.utc) + timedelta(days=3)
                                        db.commit()
                                        logger.critical(f"账户 {account.name} 因中度撞审违规已临时封禁3天")
                                        break

                except Exception as e:
                    logger.error(f"检查账户 {account.name} 违规情况时出错: {e}")
                    continue

        if not violation_found:
            logger.debug("本轮违规检测未发现问题")

    except Exception as e:
        logger.error(f"[工作流-违规检测] 发生严重错误: {str(e)}", exc_info=True)
    finally:
        logger.info("--- [工作流-违规检测] 执行完毕 ---")


def handle_material_collection(db: Session, app_settings: Dict[str, Any]):
    """
    [V62.4] 素材收集工作流：从局域网收集视频素材
    """
    logger.info("--- [工作流-素材收集] 开始：从局域网收集视频素材 ---")
    try:
        import os
        import shutil
        import threading
        from pathlib import Path
        from concurrent.futures import ThreadPoolExecutor

        # 获取配置
        collection_config = app_settings.get('workflow', {}).get('material_collection', {})
        source_dirs = collection_config.get('source_dirs', [])
        dest_dir = collection_config.get('dest_dir', 'G:/workflow_assets/01_materials_to_process/缇萃百货')
        archive_folder_name = collection_config.get('archive_folder_name', '已处理')
        video_extensions = collection_config.get('video_extensions', ['.mp4', '.mkv', '.avi', '.mov'])
        max_workers = collection_config.get('max_workers', 8)

        if not source_dirs:
            logger.info("未配置源目录，跳过素材收集")
            return

        # 确保目标目录存在
        os.makedirs(dest_dir, exist_ok=True)

        # 线程锁
        print_lock = threading.Lock()

        def safe_log(message):
            with print_lock:
                logger.info(message)

        def process_source_dir(source_dir):
            """处理单个源目录"""
            try:
                if not os.path.exists(source_dir):
                    safe_log(f"源目录不存在，跳过: {source_dir}")
                    return 0

                copied_count = 0
                archive_path = os.path.join(source_dir, archive_folder_name)

                for root, dirs, files in os.walk(source_dir):
                    # 跳过归档文件夹
                    if archive_folder_name in root:
                        continue

                    for file in files:
                        file_path = os.path.join(root, file)
                        file_ext = os.path.splitext(file)[1].lower()

                        if file_ext in video_extensions:
                            try:
                                dest_file_path = os.path.join(dest_dir, file)

                                # 避免重复复制
                                if os.path.exists(dest_file_path):
                                    safe_log(f"文件已存在，跳过: {file}")
                                    continue

                                # 复制文件
                                shutil.copy2(file_path, dest_file_path)
                                safe_log(f"复制成功: {file}")
                                copied_count += 1

                                # 移动原文件到归档文件夹
                                os.makedirs(archive_path, exist_ok=True)
                                archive_file_path = os.path.join(archive_path, file)
                                shutil.move(file_path, archive_file_path)

                            except Exception as e:
                                safe_log(f"处理文件失败 {file}: {e}")

                return copied_count

            except Exception as e:
                safe_log(f"处理源目录失败 {source_dir}: {e}")
                return 0

        # 并行处理所有源目录
        total_copied = 0
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(executor.map(process_source_dir, source_dirs))
            total_copied = sum(results)

        if total_copied > 0:
            logger.info(f"素材收集完成，共复制 {total_copied} 个文件")
        else:
            logger.info("本轮素材收集未发现新文件")

    except Exception as e:
        logger.error(f"[工作流-素材收集] 发生严重错误: {str(e)}", exc_info=True)
    finally:
        logger.info("--- [工作流-素材收集] 执行完毕 ---")


def handle_comment_management(db: Session, app_settings: Dict[str, Any]):
    """
    [New] 工作流：获取未隐藏的负面评论并进行隐藏
    """
    logger.info("--- [工作流-评论管理] 开始：管理评论（检测负面评论并隐藏） ---")
    try:
        all_principals = db.query(Principal).options(joinedload(Principal.ad_accounts)).all()

        filtered_principals = []
        if app_settings['workflow']['comment_and_violation_patrol_principals']:
            for p in all_principals:
                if p.name in app_settings['workflow']['comment_and_violation_patrol_principals']:
                    filtered_principals.append(p)
                else:
                    logger.info(f"主体 '{p.name}' 不在配置的巡检列表中，跳过评论和违规检查。")
        else:
            filtered_principals = all_principals

        if not filtered_principals:
            logger.info("没有配置需要进行评论和违规巡检的主体，或者数据库中没有匹配的主体。")
            return

        for principal in filtered_principals:
            client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=principal.id)

            for account in principal.ad_accounts:
                # 筛选条件：必须是投放账户、有抖音号、状态正常
                if account.account_type != 'DELIVERY':
                    logger.debug(f"账户 {account.name} 不是投放账户(类型: {account.account_type})，跳过评论管理。")
                    continue

                if account.status != 'active':
                    logger.debug(f"账户 {account.name} 状态异常(状态: {account.status})，跳过评论管理。")
                    continue

                if not account.aweme_id:
                    logger.debug(f"账户 {account.name} 没有绑定抖音号，跳过评论管理。")
                    continue

                if not is_account_healthy(db, client, account):
                    logger.debug(f"账户 {account.name} 健康检查未通过，跳过评论管理。")
                    continue

                logger.debug(f"正在为广告主 {account.name} (ID: {account.account_id_qc}, 抖音号: {account.aweme_id}) 获取负面评论...")
                
                comment_check_days = app_settings['workflow']['comment_check_days']
                end_time = datetime.now(timezone.utc)
                start_time = end_time - timedelta(days=comment_check_days)
                
                all_negative_comment_ids = []
                page = 1
                page_size = 100

                while True:
                    comment_list_response = client.get_comment_list(
                        advertiser_id=int(account.account_id_qc),
                        start_time=start_time.strftime('%Y-%m-%d'),
                        end_time=end_time.strftime('%Y-%m-%d'),
                        filtering={
                            "hide_status": "NOT_HIDE",
                            "emotion_type": "NEGATIVE"
                        },
                        page=page,
                        page_size=page_size
                    )

                    if not comment_list_response:
                        logger.warning(f"为广告主 {account.name} 获取评论列表失败或无数据。")
                        break
                    
                    comments = comment_list_response.get("comment_list", [])
                    if not comments:
                        logger.debug(f"广告主 {account.name} 在 {start_time.strftime('%Y-%m-%d')} 至 {end_time.strftime('%Y-%m-%d')} 期间没有未隐藏的负面评论。")
                        break
                    
                    for comment in comments:
                        all_negative_comment_ids.append(comment.get("comment_id"))
                    
                    page_info = comment_list_response.get("page_info", {})
                    if page >= page_info.get("total_page", 1):
                        break
                    page += 1
                
                if all_negative_comment_ids:
                    logger.info(f"广告主 {account.name} 发现 {len(all_negative_comment_ids)} 条未隐藏的负面评论。")
                    for i in range(0, len(all_negative_comment_ids), 20):
                        batch_comment_ids = all_negative_comment_ids[i:i+20]
                        logger.info(f"正在为广告主 {account.name} 隐藏 {len(batch_comment_ids)} 条评论: {batch_comment_ids}")
                        hide_response = client.hide_comments(
                            advertiser_id=int(account.account_id_qc),
                            comment_ids=batch_comment_ids
                        )
                        if hide_response and hide_response.get("success_comment_ids"):
                            logger.success(f"成功隐藏评论: {hide_response['success_comment_ids']}")
                        else:
                            logger.error(f"隐藏评论失败或部分失败。Response: {hide_response}")
                else:
                    logger.debug(f"广告主 {account.name} 没有需要隐藏的负面评论。")

    except Exception as e:
        logger.error(f"[工作流4] 发生严重错误: {e}", exc_info=True)
        db.rollback()


def check_test_video_global_uniqueness(local_creative_id: int, account_type: str = 'TEST') -> bool:
    """
    检查测试视频全局唯一性 - 测试视频工作流铁律

    Args:
        local_creative_id: 本地素材ID
        account_type: 账户类型，默认为'TEST'

    Returns:
        bool: True表示唯一（可以创建），False表示已存在（不能创建）
    """
    try:
        with SessionLocal() as db:
            # 获取本地素材信息
            local_creative = db.query(LocalCreative).filter(
                LocalCreative.id == local_creative_id
            ).first()

            if not local_creative:
                logger.error(f"❌ 本地素材ID {local_creative_id} 不存在")
                return False

            # 基于file_hash检查是否已在测试账户中创建过计划
            existing_campaigns_count = db.query(Campaign).join(
                campaign_platform_creative_association,
                Campaign.id == campaign_platform_creative_association.c.campaign_id
            ).join(
                PlatformCreative,
                campaign_platform_creative_association.c.platform_creative_id == PlatformCreative.id
            ).join(
                LocalCreative,
                PlatformCreative.local_creative_id == LocalCreative.id
            ).join(
                AdAccount,
                Campaign.account_id == AdAccount.id
            ).filter(
                LocalCreative.file_hash == local_creative.file_hash,
                AdAccount.account_type == account_type
            ).count()

            if existing_campaigns_count > 0:
                logger.warning(f"🚨 测试视频唯一性违规: 素材 '{local_creative.filename}' (hash: {local_creative.file_hash[:8]}...) 已在{account_type}账户中存在 {existing_campaigns_count} 个计划")
                return False

            logger.info(f"✅ 测试视频唯一性检查通过: 素材 '{local_creative.filename}' 可以创建计划")
            return True

    except Exception as e:
        logger.error(f"❌ 测试视频全局唯一性检查失败: {e}")
        # 为了安全，检查失败时返回False，阻止创建
        return False
