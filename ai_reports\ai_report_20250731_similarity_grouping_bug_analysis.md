# 相似度智能分组功能Bug分析报告

**报告时间**: 2025-07-31  
**问题严重级别**: 高  
**影响范围**: Web端手动投放功能  

## 🚨 问题描述

用户使用Web端相似度智能分组功能，指定路径 `G:\workflow_assets\03_materials_approved\缇萃百货\2025-07-30\延伸\上` 包含12个视频文件，预期创建4个计划（每组3个素材），但实际创建了6个计划。

## 📊 调查结果

### 实际创建的计划分布
通过MCP数据库查询确认，用户上午确实创建了6个计划：

1. **计划 2019**: 1个素材 (7.24王梦珂-6延伸7延伸34.mp4)
2. **计划 2016**: 2个素材 (7.24王梦珂-6延伸7延伸35.mp4 + 38.mp4)
3. **计划 2017**: 2个素材 (7.24王梦珂-6延伸7延伸36.mp4 + 40.mp4)
4. **计划 2015**: 3个素材 (7.24王梦珂-6延伸7延伸39.mp4 + 2个7.26视频)
5. **计划 2014**: 3个素材 (7.26-王梦珂-2延伸3延伸48.mp4 + 2个其他7.26视频)
6. **计划 2018**: 1个素材 (7.26-王梦珂-2延伸6延伸4.mp4)

### 视频文件分析
目标路径包含12个视频文件，按文件名模式分组：
- `7.24王梦珂-6延伸7延伸`: 6个视频
- `7.26-王梦珂-2延伸3延伸`: 1个视频  
- `7.26-王梦珂-2延伸6延伸`: 5个视频

## 🔍 根本原因分析

### 1. 相似度分组算法缺陷

**问题位置**: `tools/manual_launch.py` 第84-126行

**核心问题**: 相似度分组算法在处理小于计划容量的相似组时，逻辑有缺陷：

```python
# 问题代码段
elif len(similar_group) < creative_count_per_plan:
    current_group = similar_group.copy()
    needed = creative_count_per_plan - len(current_group)
    
    # 从剩余视频中补充
    while needed > 0 and remaining_videos:
        current_group.append(remaining_videos.pop(0))
        needed -= 1
    
    if len(current_group) >= creative_count_per_plan or not remaining_videos:
        final_groups.append(current_group)  # 这里可能创建不足3个素材的计划
```

**具体问题**:
1. 当 `not remaining_videos` 为True时，即使 `len(current_group) < creative_count_per_plan`，也会创建计划
2. 这导致了1个素材或2个素材的计划被创建
3. 违反了"每组3个素材"的基本要求

### 2. 最后剩余视频处理逻辑问题

**问题位置**: `tools/manual_launch.py` 第118-126行

```python
# 问题代码段
if remaining_videos:
    if final_groups and len(remaining_videos) < creative_count_per_plan:
        # 如果剩余视频不足一个计划，合并到最后一个计划中
        final_groups[-1].extend(remaining_videos)
    else:
        # 如果没有其他计划或剩余视频足够，创建新计划
        final_groups.append(remaining_videos)  # 这里可能创建不足3个素材的计划
```

**具体问题**:
- `else` 分支中，即使 `len(remaining_videos) < creative_count_per_plan`，也会创建新计划
- 这是导致单素材计划的主要原因

### 3. 相似度检测过于敏感

**问题位置**: `src/qianchuan_aw/utils/video_similarity.py`

**配置问题**:
- 汉明距离阈值默认为20，可能过于严格
- 导致本应归为一组的相似视频被分散到不同组

## 🛠️ 修复方案

### 方案1: 修复分组算法逻辑（推荐）

**修改文件**: `tools/manual_launch.py`

**核心修复**:
1. 确保只有达到最小素材数量的组才会被创建为计划
2. 不足的组应该合并到其他组或等待更多素材
3. 严格执行"每组至少3个素材"的约束

**具体修改**:
```python
# 修复后的逻辑
elif len(similar_group) < creative_count_per_plan:
    current_group = similar_group.copy()
    needed = creative_count_per_plan - len(current_group)
    
    # 从剩余视频中补充
    while needed > 0 and remaining_videos:
        current_group.append(remaining_videos.pop(0))
        needed -= 1
    
    # 只有达到最小数量才创建计划
    if len(current_group) >= creative_count_per_plan:
        final_groups.append(current_group)
    else:
        # 不足的视频放回剩余列表
        remaining_videos.extend(current_group)

# 修复剩余视频处理
if remaining_videos:
    if len(remaining_videos) >= creative_count_per_plan:
        # 只有足够的视频才创建新计划
        final_groups.append(remaining_videos[:creative_count_per_plan])
        remaining_videos = remaining_videos[creative_count_per_plan:]
    
    # 最后的不足视频合并到已有计划
    if remaining_videos and final_groups:
        final_groups[-1].extend(remaining_videos)
```

### 方案2: 调整相似度检测参数

**修改文件**: `config/settings.yml`

**参数调整**:
```yaml
video_similarity:
  hamming_distance_threshold: 25  # 从20调整到25，降低敏感度
  phash_size: 32
  phash_dct_crop_size: 8
```

### 方案3: 添加严格模式选项

**新增功能**: 在Web界面添加"严格分组模式"选项
- 启用时：严格按照每组N个素材分组，不允许不足数量的组
- 禁用时：允许灵活分组，但会有警告提示

## 📈 预期修复效果

### 修复前（当前问题）
- 12个视频 → 6个计划
- 计划素材分布：1,2,2,3,3,1
- 不符合预期的每组3个素材

### 修复后（预期结果）
- 12个视频 → 4个计划
- 计划素材分布：3,3,3,3
- 符合预期的每组3个素材

## 🧪 测试验证方案

### 1. 单元测试
创建针对分组算法的单元测试，覆盖各种边界情况：
- 视频数量正好整除的情况
- 视频数量有余数的情况
- 相似组大小不同的情况

### 2. 集成测试
使用用户提供的实际路径进行测试：
- 路径：`G:\workflow_assets\03_materials_approved\缇萃百货\2025-07-30\延伸\上`
- 验证修复后确实创建4个计划

### 3. 回归测试
确保修复不影响其他功能：
- 常规分组功能
- 单个视频处理
- 大批量视频处理

## 🚀 实施计划

### 第一阶段：紧急修复（1-2小时）
1. 修复 `tools/manual_launch.py` 中的分组逻辑
2. 添加详细的日志记录
3. 进行基本测试验证

### 第二阶段：完善优化（1天）
1. 调整相似度检测参数
2. 添加Web界面配置选项
3. 完善错误处理和用户提示

### 第三阶段：测试验证（1天）
1. 全面的单元测试和集成测试
2. 用户验收测试
3. 性能测试和优化

## 📋 风险评估

### 高风险
- 修改核心分组算法可能影响现有功能
- 需要充分测试确保向后兼容

### 中风险
- 参数调整可能影响相似度检测效果
- 需要平衡检测精度和分组合理性

### 低风险
- 日志记录和用户界面改进
- 不影响核心功能逻辑

## 📞 后续跟进

1. **用户反馈收集**: 修复后收集用户使用反馈
2. **性能监控**: 监控分组算法的性能表现
3. **功能优化**: 根据使用情况持续优化算法
4. **文档更新**: 更新用户使用指南和技术文档

---

**报告结论**: 相似度智能分组功能存在明确的算法缺陷，导致创建了不符合预期的计划数量。通过修复分组逻辑和调整参数，可以解决此问题并提升用户体验。建议立即实施紧急修复，确保功能正常运行。
