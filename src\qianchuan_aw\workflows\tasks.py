# -*- coding: utf-8 -*-
"""
[V50.0] Celery 任务定义模块
"""
import os
from datetime import datetime, timedelta
from qianchuan_aw.celery_app import app
from qianchuan_aw.utils.config_manager import get_config_manager
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.workflows import scheduler, fast_monitor
from qianchuan_aw.services import event_detector
from qianchuan_aw.utils.logger import logger

# 加载一次配置，供所有任务共享
config_manager = get_config_manager()
app_settings = config_manager.get_config()

def _run_task(task_func, *args, **kwargs):
    """[V52.3] 辅助函数，用于在独立的数据库会话中运行同步任务。"""
    with database_session() as db:
        task_func(db, app_settings, *args, **kwargs)

# --- [Refactor] Producer-Consumer 任务定义 ---

@app.task(name="tasks.ingest_and_upload", ignore_result=True)
def task_ingest_and_upload():
    """[Producer V2] 扫描文件并将其信息录入数据库。"""
    if not app_settings.get('workflow', {}).get('file_ingestion', {}).get('enabled', True):
        logger.info("[Task Skip] File Ingestion is disabled.")
        return
    logger.info("[Task Start] Ingest Files")

    # [V-Fix-1] 在文件摄取前先恢复失败的素材
    _run_task(scheduler.handle_failed_upload_recovery)

    # 然后执行正常的文件摄取
    _run_task(scheduler.handle_file_ingestion)
    logger.info("[Task End] Ingest Files")

@app.task(name="tasks.group_and_dispatch", ignore_result=True)
def task_group_and_dispatch():
    """[Dispatcher] 聚合素材并按组派发上传任务。"""
    if not app_settings.get('workflow', {}).get('group_dispatch', {}).get('enabled', True):
        logger.info("[Task Skip] Group Dispatch is disabled.")
        return
    logger.info("[Task Start] Group & Dispatch")
    _run_task(scheduler.group_and_dispatch_uploads)
    logger.info("[Task End] Group & Dispatch")

@app.task(name="tasks.upload_single_video", bind=True, max_retries=3, default_retry_delay=60)
def upload_single_video(self, local_creative_id: int, account_id: int, file_path: str, principal_name: str):
    """[Consumer] 处理单个视频的上传。"""
    logger.info(f"[Task Start] Uploading video: {os.path.basename(file_path)}")
    try:
        with database_session() as db:
            scheduler.process_single_video_upload(db, app_settings, local_creative_id, account_id, file_path, principal_name)
            logger.success(f"[Task Success] Video uploaded: {os.path.basename(file_path)}")
    except Exception as exc:
        logger.error(f"[Task Failed] Error uploading video {os.path.basename(file_path)}: {exc}", exc_info=True)
        # 使用Celery的重试机制
        raise self.retry(exc=exc)

@app.task(name="tasks.create_plans")
def task_create_plans():
    if not app_settings.get('workflow', {}).get('plan_creation', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Creation is disabled.")
        return
    logger.info("[Task Start] Create Plans")
    _run_task(scheduler.handle_plan_creation)
    logger.info("[Task End] Create Plans")

@app.task(name="tasks.submit_plans")
def task_submit_plans():
    if not app_settings.get('workflow', {}).get('plan_submission', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Submission is disabled.")
        return
    logger.info("[Task Start] Submit Plans")
    _run_task(scheduler.handle_plan_submission)
    logger.info("[Task End] Submit Plans")

@app.task(name="tasks.appeal_plans")
def task_appeal_plans():
    if not app_settings.get('workflow', {}).get('plan_appeal', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Appeal is disabled.")
        return
    logger.info("[Task Start] Appeal Plans")
    # 先执行全面超时检查
    _run_task(scheduler.handle_timeout_campaigns_comprehensive)
    # 再执行正常的提审流程
    _run_task(scheduler.handle_plans_awaiting_appeal)
    logger.info("[Task End] Appeal Plans")

@app.task(name="tasks.harvest_materials")
def task_harvest_materials():
    if not app_settings.get('workflow', {}).get('independent_harvest', {}).get('enabled', True):
        logger.info("[Task Skip] Independent Material Harvest is disabled.")
        return
    logger.info("[Task Start] Independent Material Harvest")
    _run_task(scheduler.handle_independent_material_harvest)
    logger.info("[Task End] Independent Material Harvest")

@app.task(name="tasks.monitor_materials")
def task_monitor_materials():
    if not app_settings.get('workflow', {}).get('material_monitoring', {}).get('enabled', True):
        logger.info("[Task Skip] Material Monitoring is disabled.")
        return
    logger.info("[Task Start] Monitor Materials")
    _run_task(scheduler.handle_monitoring_of_materials)
    logger.info("[Task End] Monitor Materials")

@app.task(name="tasks.manage_comments")
def task_manage_comments():
    # 检查是否启用评论管理
    if not app_settings.get('workflow', {}).get('comment_management', {}).get('enabled', True):
        logger.info("[Task Skip] Comment Management is disabled.")
        return
    logger.info("[Task Start] Manage Comments")
    _run_task(scheduler.handle_comment_management)
    logger.info("[Task End] Manage Comments")

@app.task(name="tasks.check_violations")
def task_check_violations():
    """独立的违规检测任务"""
    # 检查是否启用违规检测
    if not app_settings.get('workflow', {}).get('violation_detection', {}).get('enabled', True):
        logger.info("[Task Skip] Violation Detection is disabled.")
        return
    logger.info("[Task Start] Check Violations")
    _run_task(scheduler.handle_violation_detection)
    logger.info("[Task End] Check Violations")

@app.task(name="tasks.collect_materials")
def task_collect_materials():
    """素材收集任务 - 从局域网收集视频素材"""
    # 检查是否启用素材收集
    if not app_settings.get('workflow', {}).get('material_collection', {}).get('enabled', True):
        logger.info("[Task Skip] Material Collection is disabled.")
        return

    # 检查时间范围
    from datetime import datetime
    current_time = datetime.now()
    start_hour = app_settings.get('workflow', {}).get('material_collection', {}).get('start_hour', 8)
    start_minute = app_settings.get('workflow', {}).get('material_collection', {}).get('start_minute', 30)
    end_hour = app_settings.get('workflow', {}).get('material_collection', {}).get('end_hour', 20)
    end_minute = app_settings.get('workflow', {}).get('material_collection', {}).get('end_minute', 30)

    start_time = current_time.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)
    end_time = current_time.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)

    if not (start_time <= current_time <= end_time):
        logger.info(f"[Task Skip] Material Collection outside working hours ({start_hour:02d}:{start_minute:02d}-{end_hour:02d}:{end_minute:02d})")
        return

    logger.info("[Task Start] Collect Materials")
    _run_task(scheduler.handle_material_collection)
    logger.info("[Task End] Collect Materials")

@app.task(name="tasks.run_fast_monitor")
def task_run_fast_monitor():
    if not app_settings.get('lighthouse_plan', {}).get('fast_monitor_enabled', False):
        logger.info("[Task Skip] Fast Monitor is disabled.")
        return
    logger.info("[Task Start] Fast Monitor")
    _run_task(fast_monitor.fast_monitor_workflow)
    logger.info("[Task End] Fast Monitor")

@app.task(name="tasks.detect_events")
def task_detect_events():
    if not app_settings.get('lighthouse_plan', {}).get('fast_monitor_enabled', False):
        logger.info("[Task Skip] Event Detection is disabled.")
        return
    logger.info("[Task Start] Event Detection")
    with database_session() as db:
        event_detector.check_for_new_stars(db, app_settings)
        event_detector.check_for_rejected_creatives(db, app_settings)
    logger.info("[Task End] Event Detection")

@app.task(name="tasks.reset_stale_processing_status")
def task_reset_stale_processing_status():
    """
    [Watchdog Task] 定期巡检并重置卡死在 'processing' 状态的僵尸任务。
    """
    logger.info("[Watchdog] Starting stale task patrol...")
    from qianchuan_aw.utils.db_utils import database_session
    from qianchuan_aw.utils.config_loader import load_settings

    with database_session() as db:
        # 从配置文件读取超时时间
        settings = load_settings()
        timeout_minutes = settings.get('workflow', {}).get('zombie_cleanup', {}).get('timeout_minutes', 10)
        timeout_threshold = datetime.utcnow() - timedelta(minutes=timeout_minutes)

        # 查找超时的僵尸素材
        stale_creatives = db.query(LocalCreative).filter(
            LocalCreative.status == 'processing',
            LocalCreative.updated_at < timeout_threshold
        ).all()

        if not stale_creatives:
            logger.info("[Watchdog] Patrol finished. No stale tasks found.")
            return

        logger.warning(f"[Watchdog] Found {len(stale_creatives)} stale creatives. Resetting their status...")

        for creative in stale_creatives:
            logger.warning(f"  - Resetting creative ID {creative.id} (hash: {creative.file_hash}), last updated at {creative.updated_at}")
            creative.status = 'pending_grouping' # 重置为待分组状态，让它重新进入处理队列

        db.commit()
        logger.success(f"[Watchdog] Successfully reset {len(stale_creatives)} stale creatives.")
